#include "../include/cache.h"
#include "lru-cache.hpp"
#include <cstring>

extern "C" {

// Cache initialization and configuration
int lru_cache_init(const lru_cache_config_t* config) {
    if (!config) {
        return -1;
    }
    
    // For now, we assume the cache is already initialized via lru_alloc_init
    // In a full implementation, this would initialize the cache with the given config
    if (g_lru_cache) {
        return g_lru_cache->set_config(*config) ? 0 : -1;
    }
    
    return -1;
}

void lru_cache_shutdown(void) {
    if (g_lru_cache) {
        g_lru_cache->flush_all();
        g_lru_cache.reset();
    }
}

bool lru_cache_is_initialized(void) {
    return g_lru_cache != nullptr;
}

int lru_cache_set_config(const lru_cache_config_t* config) {
    if (!config || !g_lru_cache) {
        return -1;
    }
    
    return g_lru_cache->set_config(*config) ? 0 : -1;
}

void lru_cache_get_config(lru_cache_config_t* config) {
    if (!config || !g_lru_cache) {
        return;
    }
    
    *config = g_lru_cache->get_config();
}

// Core cache operations
void* lru_cache_get(void* gaddr, size_t size) {
    if (!g_lru_cache) {
        return nullptr;
    }
    
    GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
    return g_lru_cache->get(addr, size);
}

bool lru_cache_put(void* gaddr, const void* data, size_t size) {
    if (!g_lru_cache) {
        return false;
    }
    
    GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
    return g_lru_cache->put(addr, data, size);
}

bool lru_cache_remove(void* gaddr) {
    if (!g_lru_cache) {
        return false;
    }
    
    GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
    return g_lru_cache->remove(addr);
}

void lru_cache_sync(void* gaddr) {
    if (!g_lru_cache) {
        return;
    }
    
    GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
    g_lru_cache->sync(addr);
}

// Batch operations
int lru_cache_get_batch(lru_cache_request_t* requests, size_t count) {
    if (!g_lru_cache || !requests) {
        return -1;
    }
    
    return g_lru_cache->get_batch(requests, count);
}

int lru_cache_put_batch(const lru_cache_request_t* requests, size_t count) {
    if (!g_lru_cache || !requests) {
        return -1;
    }
    
    return g_lru_cache->put_batch(requests, count);
}

// Cache statistics and monitoring
void lru_cache_get_stats(lru_cache_stats_t* stats) {
    if (!stats || !g_lru_cache) {
        return;
    }
    
    g_lru_cache->get_stats(*stats);
}

void lru_cache_reset_stats(void) {
    if (g_lru_cache) {
        g_lru_cache->reset_stats();
    }
}

void lru_cache_print_detailed_stats(void) {
    if (g_lru_cache) {
        g_lru_cache->print_detailed_stats();
    }
}

// Cache management and tuning
void lru_cache_set_max_size(size_t max_size) {
    if (g_lru_cache) {
        lru_cache_config_t config = g_lru_cache->get_config();
        config.max_size = max_size;
        g_lru_cache->set_config(config);
    }
}

void lru_cache_set_max_entries(size_t max_entries) {
    if (g_lru_cache) {
        lru_cache_config_t config = g_lru_cache->get_config();
        config.max_entries = max_entries;
        g_lru_cache->set_config(config);
    }
}

void lru_cache_set_eviction_policy(lru_eviction_policy_t policy) {
    if (g_lru_cache) {
        lru_cache_config_t config = g_lru_cache->get_config();
        config.eviction_policy = policy;
        g_lru_cache->set_config(config);
    }
}

void lru_cache_set_write_policy(lru_write_policy_t policy) {
    if (g_lru_cache) {
        lru_cache_config_t config = g_lru_cache->get_config();
        config.write_policy = policy;
        g_lru_cache->set_config(config);
    }
}

// Manual cache control
bool lru_cache_evict_entry(void* gaddr) {
    if (!g_lru_cache) {
        return false;
    }
    
    GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
    return g_lru_cache->evict_specific(addr);
}

size_t lru_cache_evict_n_entries(size_t n) {
    if (!g_lru_cache) {
        return 0;
    }
    
    return g_lru_cache->evict_n_entries(n);
}

void lru_cache_evict_by_type(uint8_t type_id) {
    if (g_lru_cache) {
        g_lru_cache->evict_by_type(type_id);
    }
}

void lru_cache_evict_old_entries(uint64_t max_age_ms) {
    if (g_lru_cache) {
        g_lru_cache->evict_old_entries(max_age_ms);
    }
}

// Cache warming and prefetching
void lru_cache_warm_up(void** gaddrs, size_t* sizes, size_t count) {
    if (!g_lru_cache || !gaddrs || !sizes) {
        return;
    }
    
    for (size_t i = 0; i < count; i++) {
        if (gaddrs[i]) {
            GlobalAddr addr = GlobalAddr::fromPointer(gaddrs[i]);
            g_lru_cache->prefetch(addr, sizes[i]);
        }
    }
}

void lru_cache_prefetch_sequential(void* start_gaddr, size_t total_size, size_t block_size) {
    if (!g_lru_cache) {
        return;
    }
    
    GlobalAddr start_addr = GlobalAddr::fromPointer(start_gaddr);
    g_lru_cache->prefetch_sequential(start_addr, total_size, block_size);
}

void lru_cache_prefetch_pattern(void** gaddrs, size_t count) {
    if (!g_lru_cache || !gaddrs) {
        return;
    }
    
    std::vector<GlobalAddr> addrs;
    addrs.reserve(count);
    
    for (size_t i = 0; i < count; i++) {
        if (gaddrs[i]) {
            addrs.push_back(GlobalAddr::fromPointer(gaddrs[i]));
        }
    }
    
    g_lru_cache->prefetch_pattern(addrs);
}

// Cache coherence and consistency
void lru_cache_invalidate_range(void* start_gaddr, size_t size) {
    if (!g_lru_cache) {
        return;
    }
    
    GlobalAddr start_addr = GlobalAddr::fromPointer(start_gaddr);
    g_lru_cache->invalidate_range(start_addr, size);
}

void lru_cache_flush_range(void* start_gaddr, size_t size) {
    if (!g_lru_cache) {
        return;
    }
    
    GlobalAddr start_addr = GlobalAddr::fromPointer(start_gaddr);
    g_lru_cache->flush_range(start_addr, size);
}

void lru_cache_barrier(void) {
    if (g_lru_cache) {
        g_lru_cache->barrier();
    }
}

// Advanced features
void lru_cache_set_eviction_callback(lru_cache_callback_t callback, void* user_data) {
    if (g_lru_cache) {
        g_lru_cache->set_eviction_callback(callback, user_data);
    }
}

void lru_cache_set_miss_callback(lru_cache_callback_t callback, void* user_data) {
    if (g_lru_cache) {
        g_lru_cache->set_miss_callback(callback, user_data);
    }
}

// Cache debugging and diagnostics
void lru_cache_dump_entries(void) {
    if (g_lru_cache) {
        g_lru_cache->dump_entries();
    }
}

void lru_cache_validate_consistency(void) {
    if (g_lru_cache) {
        g_lru_cache->validate_consistency();
    }
}

bool lru_cache_check_integrity(void) {
    if (!g_lru_cache) {
        return false;
    }
    
    return g_lru_cache->check_integrity();
}

void lru_cache_print_lru_order(void) {
    if (g_lru_cache) {
        g_lru_cache->print_lru_order();
    }
}

// Performance profiling
void lru_cache_enable_tracing(bool enable) {
    if (g_lru_cache) {
        g_lru_cache->enable_tracing(enable);
    }
}

size_t lru_cache_get_trace(lru_cache_trace_entry_t* buffer, size_t buffer_size) {
    if (!g_lru_cache) {
        return 0;
    }
    
    return g_lru_cache->get_trace(buffer, buffer_size);
}

void lru_cache_clear_trace(void) {
    if (g_lru_cache) {
        g_lru_cache->clear_trace();
    }
}

}  // extern "C"
