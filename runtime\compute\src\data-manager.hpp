#ifndef COMPUTE_DATA_MANAGER_H
#define COMPUTE_DATA_MANAGER_H

#include <stdbool.h>
#include <stdint.h>
#include <atomic>
#include <cstddef>
#include <list>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <sys/resource.h>

#include "addr.h"
#include "rdma.hpp"

#ifndef COMPUTE_CACHE_LIMIT
#define COMPUTE_CACHE_LIMIT (1024 * 1024 * 1024)  // 1GB
#endif

struct AddressDep {
    std::list<GlobalAddr> children;
    size_t children_num;
    std::list<GlobalAddr> parents;

    AddressDep() = default;
};

struct NodeType {
    size_t freq;
    std::mutex mutex;  // for thread safety
    void* local_addr;
    std::list<GlobalAddr> children;
    size_t cache_children_num;
    std::list<GlobalAddr> parents;
    NodeType() : freq(0), local_addr(nullptr), cache_children_num(0), 
                 children(), parents(), mutex() {
        // Initialize the node with default values
    }
};

struct DataType {
    size_t size;
    std::vector<uint64_t> chunk_next_addrs;
    DataType(size_t size) : size(size) {}
};

class DataManager {
    RDMAClient* rdma;

    // Memory allocation
    std::mutex type_mutex;
    std::vector<DataType> types;
    std::mutex cache_leaf_mutex;
    std::vector<std::list<GlobalAddr>> cache_leaf;
    std::unordered_map<size_t, uint8_t> size_to_type_id;

    std::mutex addr_table_mutex;
    std::unordered_map<GlobalAddr, NodeType*> addr_table;
    std::mutex acc_dep_mutex;
    std::unordered_map<GlobalAddr, GlobalAddr> temp_access_dep;
    std::atomic_uint64_t next_chunk_addr;
    std::atomic<size_t> cache_size{0};
    size_t chunk_size() { return rdma->mem().page_size; }

    GlobalAddr get_orig_global_addr(GlobalAddr addr) {
        return {.typeID = addr.typeID, .offset = addr.offset - get_offset(addr)};
    }

    size_t get_size(GlobalAddr addr) {
        // Get the size of the data type based on the type ID
        std::lock_guard<std::mutex> type_lock(type_mutex);
        if (addr.typeID >= types.size()) {
            throw std::runtime_error("Invalid type ID");
        }
        return types[addr.typeID].size;
    }

    size_t get_offset(GlobalAddr addr) {
        // Get the offset of the data type based on the type ID
        std::lock_guard<std::mutex> type_lock(type_mutex);
        if (addr.typeID >= types.size()) {
            return addr.offset;
        }
        size_t size = types[addr.typeID].size;
        if (size == 0) {
            return addr.offset;
        }
        return (addr.offset % chunk_size()) % size;
    }

    uint8_t get_type_id(size_t size) {
        std::lock_guard<std::mutex> type_lock(type_mutex);
        auto it = size_to_type_id.find(size);
        if (it != size_to_type_id.end()) {
            // If type with the same size exists, reuse that type
            return it->second;
        }
        // else, register new type
        auto new_type = DataType(size);
        types.push_back(new_type);
        auto index = types.size() - 1;
        size_to_type_id[size] = index;

        {
            std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
            cache_leaf.push_back(std::list<GlobalAddr>());
        }
        return index;
    }
    
    void update_cache_leaf_remove(GlobalAddr gaddr) {
        // Update the cache leaf
        NodeType* node = nullptr;
        std::vector<GlobalAddr> parents_to_update;

        {
            std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
            auto it = addr_table.find(gaddr);
            if (it == addr_table.end()) {
                return;  // Address not found in the address table
            }
            node = it->second;
        }

        if (node == nullptr) {
            return;  // Address not found in the address table
        }

        {
            std::lock_guard<std::mutex> node_lock(node->mutex);
            parents_to_update.assign(node->parents.begin(), node->parents.end());
        }

        for (const auto& parent : parents_to_update) {
            std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
            auto it = addr_table.find(parent);
            if (it != addr_table.end() && it->second != nullptr) {
                std::lock_guard<std::mutex> parent_lock(it->second->mutex);
                auto& x = it->second->cache_children_num;
                x--;
                if (x == 0) {
                    std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
                    cache_leaf[parent.typeID].push_back(parent);  // Add to leaf if no children
                }
            }
        }

        {
            std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
            cache_leaf[gaddr.typeID].remove(gaddr);  // Remove from the cache leaf
        }
    }

    void update_cache_leaf_insert(GlobalAddr gaddr) {
        // Update the cache leaf
        NodeType* node = nullptr;
        std::vector<GlobalAddr> parents_to_update;

        {
            std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
            auto it = addr_table.find(gaddr);
            if (it == addr_table.end()) {
                return;  // Address not found in the address table
            }
            node = it->second;
        }

        if (node == nullptr) {
            return;  // Address not found in the address table
        }

        {
            std::lock_guard<std::mutex> node_lock(node->mutex);
            parents_to_update.assign(node->parents.begin(), node->parents.end());
        }

        for (const auto& parent : parents_to_update) {
            std::lock_guard<std::mutex> addr_lock(addr_table_mutex);
            auto it = addr_table.find(parent);
            if (it != addr_table.end() && it->second != nullptr) {
                std::lock_guard<std::mutex> parent_lock(it->second->mutex);
                auto& x = it->second->cache_children_num;
                x++;
                if (x == 1) {
                    std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
                    cache_leaf[parent.typeID].remove(parent);  // Remove from leaf if it has children
                }
            }
        }

        {
            std::lock_guard<std::mutex> cache_lock(cache_leaf_mutex);
            cache_leaf[gaddr.typeID].push_back(gaddr);  // Add to the cache leaf
        }
    }

    inline bool is_cache_full(size_t size) {
        // Check if the cache size exceeds the limit
        // return cache_size + size > COMPUTE_CACHE_LIMIT;
        struct rusage usage;
        getrusage(RUSAGE_SELF, &usage);
        size_t mem_usage = usage.ru_maxrss * 1024;  // Convert to bytes
        return mem_usage + size > COMPUTE_CACHE_LIMIT;
    }

   public:
    // std::mutex mutex;  // let's make it public for now

    void disaggFree(GlobalAddr gaddr);
    GlobalAddr disaggAlloc(size_t size);
    void addAddrDep(GlobalAddr addr_u, GlobalAddr addr_v);  // u -> v
    void acceptAddrDep(GlobalAddr addr);
    void* getLocalAddr(GlobalAddr gaddr);
    bool cacheInsert(GlobalAddr gaddr);

    DataManager(RDMAClient* rdma) : rdma(rdma), type_mutex(), types(), cache_leaf_mutex(),
                                   cache_leaf(), size_to_type_id(), addr_table_mutex(),
                                   temp_access_dep(), next_chunk_addr(0), cache_size(0) {
        // Initialize the data manager
        types.push_back(DataType(0));  // 0 is invalid type
        cache_leaf.push_back(std::list<GlobalAddr>());
        next_chunk_addr = rdma->mem().addr;
    }
};

#endif
