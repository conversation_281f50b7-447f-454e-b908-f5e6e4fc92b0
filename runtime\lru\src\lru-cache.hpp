#ifndef LRU_CACHE_IMPL_HPP
#define LRU_CACHE_IMPL_HPP

#include <cstddef>
#include <cstdint>
#include <list>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <chrono>

#include "../include/addr.h"
#include "../include/cache.h"
#include "../../common/rdma.h"
#include "../../common/handshake.h"

// Forward declarations
struct rdma_connection;

// Cache entry representing a cached memory page
struct LRUCacheEntry {
    GlobalAddr addr;                    // Global address of the cached data
    void* local_addr;                   // Local memory address where data is cached
    size_t size;                        // Size of the cached data
    uint64_t access_count;              // Number of times this entry has been accessed
    std::chrono::steady_clock::time_point last_access;  // Last access time
    bool dirty;                         // Whether the cached data has been modified
    bool pinned;                        // Whether the entry is pinned in cache
    uint8_t type_id;                    // Memory type identifier
    std::mutex entry_mutex;             // Mutex for thread-safe access to this entry

    LRUCacheEntry(GlobalAddr addr, void* local_addr, size_t size, uint8_t type_id);
    ~LRUCacheEntry();

    // Disable copy constructor and assignment operator
    LRUCacheEntry(const LRUCacheEntry&) = delete;
    LRUCacheEntry& operator=(const LRUCacheEntry&) = delete;

    void touch();                       // Update access time and count
    bool is_expired(uint64_t max_age_ms) const;
};

// RDMA client wrapper for remote memory operations
class RDMAManager {
private:
    struct rdma_connection* conn;
    struct memory_info mem_info;
    void* rdma_buffer;                  // Registered RDMA buffer
    struct ibv_mr* rdma_mr;             // Memory region for RDMA buffer
    size_t buffer_size;
    std::mutex rdma_mutex;              // Mutex for RDMA operations

public:
    explicit RDMAManager(const char* memory_server_addr);
    ~RDMAManager();

    // Disable copy constructor and assignment operator
    RDMAManager(const RDMAManager&) = delete;
    RDMAManager& operator=(const RDMAManager&) = delete;

    // RDMA operations
    bool read(GlobalAddr from, void* to, size_t size);
    bool write(const void* from, GlobalAddr to, size_t size);
    bool read_async(GlobalAddr from, void* to, size_t size);
    bool write_async(const void* from, GlobalAddr to, size_t size);
    
    // Memory info
    const struct memory_info& get_memory_info() const { return mem_info; }
    size_t get_page_size() const { return mem_info.page_size; }
    uint64_t get_base_addr() const { return mem_info.addr; }
};

// Main LRU Cache implementation
class LRUCache {
private:
    // RDMA manager for remote operations
    std::unique_ptr<RDMAManager> rdma_manager;
    
    // Cache configuration
    lru_cache_config_t config;
    
    // LRU data structures
    std::list<GlobalAddr> lru_list;                                    // LRU list (most recent at front)
    std::unordered_map<GlobalAddr, std::list<GlobalAddr>::iterator> lru_map;  // Fast lookup for LRU positions
    std::unordered_map<GlobalAddr, std::unique_ptr<LRUCacheEntry>> cache_entries;  // Cache entries by address
    
    // Cache management
    size_t current_size;                // Current cache size in bytes
    size_t current_entries;             // Current number of entries
    
    // Statistics
    lru_cache_stats_t stats;
    std::chrono::steady_clock::time_point start_time;
    
    // Thread safety
    mutable std::mutex cache_mutex;     // Main cache mutex
    mutable std::mutex stats_mutex;     // Statistics mutex
    
    // Tracing and debugging
    bool tracing_enabled;
    std::vector<lru_cache_trace_entry_t> trace_buffer;
    std::mutex trace_mutex;
    
    // Callbacks
    lru_cache_callback_t eviction_callback;
    void* eviction_callback_data;
    lru_cache_callback_t miss_callback;
    void* miss_callback_data;
    
    // Private helper methods
    void move_to_front(GlobalAddr addr);
    void remove_from_lru(GlobalAddr addr);
    bool evict_lru_entry();
    bool evict_entry(GlobalAddr addr);
    void update_statistics(bool hit, size_t size, uint64_t latency_ns);
    bool is_cache_full(size_t additional_size) const;
    bool should_evict() const;
    GlobalAddr find_eviction_candidate();
    void add_trace_entry(GlobalAddr addr, size_t size, bool hit, uint64_t latency_ns);
    
    // Memory management helpers
    void* allocate_local_memory(size_t size);
    void free_local_memory(void* ptr);
    
public:
    explicit LRUCache(const char* memory_server_addr);
    ~LRUCache();
    
    // Disable copy constructor and assignment operator
    LRUCache(const LRUCache&) = delete;
    LRUCache& operator=(const LRUCache&) = delete;
    
    // Configuration
    bool set_config(const lru_cache_config_t& new_config);
    const lru_cache_config_t& get_config() const { return config; }
    
    // Core cache operations
    void* get(GlobalAddr addr, size_t size);
    bool put(GlobalAddr addr, const void* data, size_t size);
    bool remove(GlobalAddr addr);
    void sync(GlobalAddr addr);
    bool contains(GlobalAddr addr) const;
    
    // Batch operations
    int get_batch(lru_cache_request_t* requests, size_t count);
    int put_batch(const lru_cache_request_t* requests, size_t count);
    
    // Cache management
    void clear();
    void flush_all();
    void flush(GlobalAddr addr);
    void touch(GlobalAddr addr);
    void mark_dirty(GlobalAddr addr);
    void pin(GlobalAddr addr);
    void unpin(GlobalAddr addr);
    
    // Eviction control
    bool evict_specific(GlobalAddr addr);
    size_t evict_n_entries(size_t n);
    void evict_by_type(uint8_t type_id);
    void evict_old_entries(uint64_t max_age_ms);
    
    // Prefetching
    void prefetch(GlobalAddr addr, size_t size);
    void prefetch_sequential(GlobalAddr start_addr, size_t total_size, size_t block_size);
    void prefetch_pattern(const std::vector<GlobalAddr>& addrs);
    
    // Statistics and monitoring
    void get_stats(lru_cache_stats_t& stats) const;
    void reset_stats();
    void print_stats() const;
    void print_detailed_stats() const;
    
    // Cache coherence
    void invalidate_range(GlobalAddr start_addr, size_t size);
    void flush_range(GlobalAddr start_addr, size_t size);
    void barrier();
    
    // Callbacks
    void set_eviction_callback(lru_cache_callback_t callback, void* user_data);
    void set_miss_callback(lru_cache_callback_t callback, void* user_data);
    
    // Debugging and diagnostics
    void dump_entries() const;
    bool validate_consistency() const;
    bool check_integrity() const;
    void print_lru_order() const;
    
    // Tracing
    void enable_tracing(bool enable);
    size_t get_trace(lru_cache_trace_entry_t* buffer, size_t buffer_size);
    void clear_trace();
};

// Global cache instance
extern std::unique_ptr<LRUCache> g_lru_cache;

// Helper functions
uint64_t get_timestamp_ns();
size_t align_size(size_t size, size_t alignment);
bool is_power_of_two(size_t value);

#endif  // LRU_CACHE_IMPL_HPP
