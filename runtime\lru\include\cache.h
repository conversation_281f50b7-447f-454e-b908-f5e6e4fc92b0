#ifndef LRU_CACHE_H
#define LRU_CACHE_H

#include <stddef.h>
#include <stdint.h>
#include <stdbool.h>
#include "addr.h"

#ifdef __cplusplus
extern "C" {
#endif

// Cache configuration and policies
typedef enum {
    LRU_POLICY_STRICT_LRU = 0,   // Strict LRU eviction
    LRU_POLICY_LRU_K = 1,        // LRU-K algorithm
    LRU_POLICY_ARC = 2,          // Adaptive Replacement Cache
    LRU_POLICY_CLOCK = 3,        // Clock algorithm
    LRU_POLICY_RANDOM = 4        // Random eviction
} lru_eviction_policy_t;

typedef enum {
    LRU_WRITE_THROUGH = 0,       // Write through to remote memory
    LRU_WRITE_BACK = 1,          // Write back on eviction
    LRU_WRITE_AROUND = 2         // Bypass cache on writes
} lru_write_policy_t;

typedef struct {
    size_t max_size;             // Maximum cache size in bytes
    size_t max_entries;          // Maximum number of entries
    lru_eviction_policy_t eviction_policy;
    lru_write_policy_t write_policy;
    bool enable_prefetch;        // Enable automatic prefetching
    size_t prefetch_distance;    // Prefetch distance in bytes
    bool enable_compression;     // Enable data compression
    double compression_ratio;    // Target compression ratio
} lru_cache_config_t;

// Cache initialization and configuration
int lru_cache_init(const lru_cache_config_t* config);
void lru_cache_shutdown(void);
bool lru_cache_is_initialized(void);

int lru_cache_set_config(const lru_cache_config_t* config);
void lru_cache_get_config(lru_cache_config_t* config);

// Core cache operations
void* lru_cache_get(void* gaddr, size_t size);
bool lru_cache_put(void* gaddr, const void* data, size_t size);
bool lru_cache_remove(void* gaddr);
void lru_cache_sync(void* gaddr);            // Synchronize with remote memory

// Batch operations for efficiency
typedef struct {
    void* gaddr;
    size_t size;
    void* local_addr;            // Output: local address if cached
    bool hit;                    // Output: whether it was a cache hit
} lru_cache_request_t;

int lru_cache_get_batch(lru_cache_request_t* requests, size_t count);
int lru_cache_put_batch(const lru_cache_request_t* requests, size_t count);

// Cache statistics and monitoring
typedef struct {
    size_t current_size;         // Current cache size in bytes
    size_t max_size;             // Maximum cache size
    size_t entry_count;          // Number of cached entries
    size_t max_entries;          // Maximum number of entries
    
    uint64_t total_requests;     // Total cache requests
    uint64_t cache_hits;         // Number of cache hits
    uint64_t cache_misses;       // Number of cache misses
    uint64_t evictions;          // Number of evictions
    uint64_t writebacks;         // Number of write-backs
    
    double hit_rate;             // Cache hit rate
    double miss_rate;            // Cache miss rate
    double eviction_rate;        // Eviction rate
    
    uint64_t bytes_read;         // Total bytes read from remote
    uint64_t bytes_written;      // Total bytes written to remote
    uint64_t rdma_reads;         // Number of RDMA read operations
    uint64_t rdma_writes;        // Number of RDMA write operations
    
    double avg_access_time;      // Average access time in microseconds
    double avg_miss_penalty;     // Average miss penalty in microseconds
} lru_cache_stats_t;

void lru_cache_get_stats(lru_cache_stats_t* stats);
void lru_cache_reset_stats(void);
void lru_cache_print_detailed_stats(void);

// Cache management and tuning
void lru_cache_set_max_size(size_t max_size);
void lru_cache_set_max_entries(size_t max_entries);
void lru_cache_set_eviction_policy(lru_eviction_policy_t policy);
void lru_cache_set_write_policy(lru_write_policy_t policy);

// Manual cache control
bool lru_cache_evict_entry(void* gaddr);     // Evict specific entry
size_t lru_cache_evict_n_entries(size_t n);  // Evict N entries
void lru_cache_evict_by_type(uint8_t type_id); // Evict entries of specific type
void lru_cache_evict_old_entries(uint64_t max_age_ms); // Evict old entries

// Cache warming and prefetching
void lru_cache_warm_up(void** gaddrs, size_t* sizes, size_t count);
void lru_cache_prefetch_sequential(void* start_gaddr, size_t total_size, size_t block_size);
void lru_cache_prefetch_pattern(void** gaddrs, size_t count);

// Cache coherence and consistency
void lru_cache_invalidate_range(void* start_gaddr, size_t size);
void lru_cache_flush_range(void* start_gaddr, size_t size);
void lru_cache_barrier(void);                // Memory barrier for cache operations

// Advanced features
typedef void (*lru_cache_callback_t)(void* gaddr, void* data, size_t size, void* user_data);

void lru_cache_set_eviction_callback(lru_cache_callback_t callback, void* user_data);
void lru_cache_set_miss_callback(lru_cache_callback_t callback, void* user_data);

// Cache debugging and diagnostics
void lru_cache_dump_entries(void);
void lru_cache_validate_consistency(void);
bool lru_cache_check_integrity(void);
void lru_cache_print_lru_order(void);

// Performance profiling
typedef struct {
    uint64_t timestamp;
    void* gaddr;
    size_t size;
    bool hit;
    uint64_t latency_ns;
} lru_cache_trace_entry_t;

void lru_cache_enable_tracing(bool enable);
size_t lru_cache_get_trace(lru_cache_trace_entry_t* buffer, size_t buffer_size);
void lru_cache_clear_trace(void);

#ifdef __cplusplus
}
#endif

#endif  // LRU_CACHE_H
