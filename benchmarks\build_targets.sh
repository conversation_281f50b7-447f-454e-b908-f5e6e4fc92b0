#!/bin/bash

# Build script for new Pentathlon benchmark targets
# Demonstrates the new make options: benchmark-optimized and lru

set -e

SCRIPT_DIR="$(dirname "$0")"
BUILD_DIR="$SCRIPT_DIR/build"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

show_help() {
    echo "Usage: $0 [TARGET] [OPTIONS]"
    echo ""
    echo "New Make Targets:"
    echo "  benchmark-optimized  - Links with libskiplist_optimized.a + libdm_compiler_rt_compute.a"
    echo "  lru                  - Links with libskiplist_optimized.a + lru-cache"
    echo ""
    echo "Other Targets:"
    echo "  benchmark-skiplist   - Skiplist only"
    echo "  benchmark-compute    - Compute runtime only"
    echo "  benchmark-all        - All components"
    echo "  clean                - Clean build directory"
    echo ""
    echo "Options:"
    echo "  -h, --help           - Show this help"
    echo "  -v, --verbose        - Verbose output"
    echo "  -j N                 - Number of parallel jobs"
    echo ""
    echo "Examples:"
    echo "  $0 benchmark-optimized"
    echo "  $0 lru -j 8"
    echo "  $0 benchmark-all --verbose"
}

# Parse arguments
TARGET=""
VERBOSE=false
JOBS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -j)
            JOBS="$2"
            shift 2
            ;;
        benchmark-optimized|lru|benchmark-skiplist|benchmark-compute|benchmark-all|clean)
            TARGET="$1"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

if [[ -z "$TARGET" ]]; then
    print_error "No target specified"
    show_help
    exit 1
fi

# Auto-detect number of jobs
if [[ -z "$JOBS" ]]; then
    if command -v nproc &> /dev/null; then
        JOBS=$(nproc)
    else
        JOBS=4
    fi
fi

print_header "Pentathlon Benchmark Build Targets"
echo "Target: $TARGET"
echo "Jobs: $JOBS"
echo "Verbose: $VERBOSE"
echo ""

# Clean build
if [[ "$TARGET" == "clean" ]]; then
    print_header "Cleaning Build Directory"
    
    if [[ -d "$BUILD_DIR" ]]; then
        rm -rf "$BUILD_DIR"
        print_success "Build directory cleaned"
    else
        print_warning "Build directory does not exist"
    fi
    exit 0
fi

# Check if CMake is available
if ! command -v cmake &> /dev/null; then
    print_error "CMake not found. Please install CMake 3.20 or later."
    exit 1
fi

# Create build directory
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

print_header "Configuring with CMake"

# Configure based on target
case "$TARGET" in
    benchmark-optimized)
        CMAKE_ARGS=(
            "-DPENTATHLON_LINK_SKIPLIST=ON"
            "-DPENTATHLON_LINK_COMPUTE=ON"
            "-DPENTATHLON_LINK_LRU=OFF"
        )
        MAKE_TARGET="benchmark-optimized"
        EXECUTABLE="pentathlon-bm-local-optimized"
        DESCRIPTION="Optimized benchmark (libskiplist_optimized.a + libdm_compiler_rt_compute.a)"
        ;;
    lru)
        CMAKE_ARGS=(
            "-DPENTATHLON_LINK_SKIPLIST=ON"
            "-DPENTATHLON_LINK_COMPUTE=OFF"
            "-DPENTATHLON_LINK_LRU=ON"
        )
        MAKE_TARGET="lru"
        EXECUTABLE="pentathlon-bm-local-lru"
        DESCRIPTION="LRU benchmark (libskiplist_optimized.a + lru-cache)"
        ;;
    benchmark-skiplist)
        CMAKE_ARGS=(
            "-DPENTATHLON_LINK_SKIPLIST=ON"
            "-DPENTATHLON_LINK_COMPUTE=OFF"
            "-DPENTATHLON_LINK_LRU=OFF"
        )
        MAKE_TARGET="benchmark-skiplist"
        EXECUTABLE="pentathlon-bm-local"
        DESCRIPTION="Skiplist only benchmark"
        ;;
    benchmark-compute)
        CMAKE_ARGS=(
            "-DPENTATHLON_LINK_SKIPLIST=OFF"
            "-DPENTATHLON_LINK_COMPUTE=ON"
            "-DPENTATHLON_LINK_LRU=OFF"
        )
        MAKE_TARGET="benchmark-compute"
        EXECUTABLE="pentathlon-bm-local"
        DESCRIPTION="Compute runtime only benchmark"
        ;;
    benchmark-all)
        CMAKE_ARGS=(
            "-DPENTATHLON_LINK_SKIPLIST=ON"
            "-DPENTATHLON_LINK_COMPUTE=ON"
            "-DPENTATHLON_LINK_LRU=ON"
        )
        MAKE_TARGET="benchmark-all"
        EXECUTABLE="pentathlon-bm-local"
        DESCRIPTION="All components benchmark"
        ;;
    *)
        print_error "Unknown target: $TARGET"
        exit 1
        ;;
esac

echo "Building: $DESCRIPTION"
echo ""

# Configure
if [[ "$VERBOSE" == "true" ]]; then
    echo "CMake args: ${CMAKE_ARGS[*]}"
    cmake "${CMAKE_ARGS[@]}" "$SCRIPT_DIR"
else
    cmake "${CMAKE_ARGS[@]}" "$SCRIPT_DIR" > /dev/null
fi

print_success "Configuration completed"

# Build
echo "Building target '$MAKE_TARGET' with $JOBS jobs..."
if [[ "$VERBOSE" == "true" ]]; then
    make -j "$JOBS" "$MAKE_TARGET"
else
    make -j "$JOBS" "$MAKE_TARGET" > /dev/null
fi

print_success "Build completed"

# Check executable
if [[ -f "$EXECUTABLE" ]]; then
    local size=$(stat -c%s "$EXECUTABLE" 2>/dev/null || stat -f%z "$EXECUTABLE" 2>/dev/null || echo "unknown")
    echo "Executable: $BUILD_DIR/$EXECUTABLE ($size bytes)"
else
    print_warning "Executable not found: $EXECUTABLE"
fi

cd - > /dev/null

print_header "Build Summary"
echo "Target: $TARGET"
echo "Description: $DESCRIPTION"
echo "Build directory: $BUILD_DIR"
echo "Executable: $BUILD_DIR/$EXECUTABLE"
echo ""
echo "To run the benchmark:"
echo "  $BUILD_DIR/$EXECUTABLE [options]"
echo ""
echo "Available targets:"
echo "  benchmark-optimized  - libskiplist_optimized.a + libdm_compiler_rt_compute.a"
echo "  lru                  - libskiplist_optimized.a + lru-cache"
echo "  benchmark-skiplist   - Skiplist only"
echo "  benchmark-compute    - Compute runtime only"
echo "  benchmark-all        - All components"
echo ""
echo "Note: For benchmark-optimized and lru targets, make sure to:"
echo "  1. Generate optimized skiplist: cd scripts && ./compile.sh"
echo "  2. Build runtime libraries: ./prepare_runtime_libs.sh"

print_success "Build script completed successfully!"
