#!/bin/bash

# Script to install RDMA dependencies for Pentathlon benchmark
# Supports Ubuntu/Debian, CentOS/RHEL, and Fedora

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$ID
        VERSION=$VERSION_ID
    elif [[ -f /etc/redhat-release ]]; then
        OS="centos"
    elif [[ -f /etc/debian_version ]]; then
        OS="debian"
    else
        OS="unknown"
    fi
}

# Install dependencies based on OS
install_dependencies() {
    case "$OS" in
        ubuntu|debian)
            print_header "Installing RDMA dependencies on Ubuntu/Debian"
            
            echo "Updating package list..."
            sudo apt-get update
            
            echo "Installing RDMA development libraries..."
            sudo apt-get install -y \
                libibverbs-dev \
                librdmacm-dev \
                cmake \
                build-essential \
                pkg-config
            
            print_success "RDMA dependencies installed successfully"
            ;;
            
        centos|rhel|rocky|almalinux)
            print_header "Installing RDMA dependencies on CentOS/RHEL"
            
            echo "Installing RDMA development libraries..."
            sudo yum install -y \
                libibverbs-devel \
                librdmacm-devel \
                cmake \
                gcc \
                gcc-c++ \
                pkgconfig
            
            print_success "RDMA dependencies installed successfully"
            ;;
            
        fedora)
            print_header "Installing RDMA dependencies on Fedora"
            
            echo "Installing RDMA development libraries..."
            sudo dnf install -y \
                libibverbs-devel \
                librdmacm-devel \
                cmake \
                gcc \
                gcc-c++ \
                pkgconf-pkg-config
            
            print_success "RDMA dependencies installed successfully"
            ;;
            
        arch|manjaro)
            print_header "Installing RDMA dependencies on Arch Linux"
            
            echo "Installing RDMA development libraries..."
            sudo pacman -S --noconfirm \
                rdma-core \
                cmake \
                base-devel \
                pkgconf
            
            print_success "RDMA dependencies installed successfully"
            ;;
            
        *)
            print_error "Unsupported operating system: $OS"
            echo ""
            echo "Please install the following packages manually:"
            echo "  - libibverbs development headers"
            echo "  - librdmacm development headers"
            echo "  - CMake 3.20+"
            echo "  - C/C++ compiler"
            echo "  - pkg-config"
            exit 1
            ;;
    esac
}

# Verify installation
verify_installation() {
    print_header "Verifying Installation"
    
    # Check if pkg-config can find the libraries
    if pkg-config --exists libibverbs; then
        print_success "libibverbs found"
        echo "  Version: $(pkg-config --modversion libibverbs)"
        echo "  Cflags: $(pkg-config --cflags libibverbs)"
        echo "  Libs: $(pkg-config --libs libibverbs)"
    else
        print_error "libibverbs not found via pkg-config"
    fi
    
    echo ""
    
    if pkg-config --exists librdmacm; then
        print_success "librdmacm found"
        echo "  Version: $(pkg-config --modversion librdmacm)"
        echo "  Cflags: $(pkg-config --cflags librdmacm)"
        echo "  Libs: $(pkg-config --libs librdmacm)"
    else
        print_error "librdmacm not found via pkg-config"
    fi
    
    echo ""
    
    # Check CMake version
    if command -v cmake &> /dev/null; then
        CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
        print_success "CMake found: $CMAKE_VERSION"
    else
        print_error "CMake not found"
    fi
    
    echo ""
    
    # Check compiler
    if command -v gcc &> /dev/null; then
        GCC_VERSION=$(gcc --version | head -n1)
        print_success "GCC found: $GCC_VERSION"
    else
        print_warning "GCC not found"
    fi
    
    if command -v g++ &> /dev/null; then
        GPP_VERSION=$(g++ --version | head -n1)
        print_success "G++ found: $GPP_VERSION"
    else
        print_warning "G++ not found"
    fi
}

# Test build
test_build() {
    print_header "Testing Build Configuration"
    
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Create a simple test CMakeLists.txt
    cat > CMakeLists.txt << 'EOF'
cmake_minimum_required(VERSION 3.20)
project(rdma_test)

find_package(PkgConfig REQUIRED)
pkg_check_modules(IBVERBS REQUIRED libibverbs)
pkg_check_modules(RDMACM REQUIRED librdmacm)

message(STATUS "IBVERBS found: ${IBVERBS_FOUND}")
message(STATUS "RDMACM found: ${RDMACM_FOUND}")
message(STATUS "IBVERBS libraries: ${IBVERBS_LIBRARIES}")
message(STATUS "RDMACM libraries: ${RDMACM_LIBRARIES}")
EOF
    
    # Test CMake configuration
    if cmake . > cmake_output.log 2>&1; then
        print_success "CMake configuration test passed"
        echo "CMake output:"
        grep "STATUS" cmake_output.log | sed 's/^/  /'
    else
        print_error "CMake configuration test failed"
        echo "CMake output:"
        cat cmake_output.log | sed 's/^/  /'
    fi
    
    # Cleanup
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
}

# Main function
main() {
    print_header "Pentathlon RDMA Dependencies Installer"
    
    # Check if running as root
    if [[ $EUID -eq 0 ]]; then
        print_warning "Running as root. This script will use sudo when needed."
    fi
    
    # Detect OS
    detect_os
    echo "Detected OS: $OS"
    
    if [[ "$OS" == "unknown" ]]; then
        print_error "Could not detect operating system"
        exit 1
    fi
    
    echo ""
    
    # Install dependencies
    install_dependencies
    
    echo ""
    
    # Verify installation
    verify_installation
    
    echo ""
    
    # Test build
    test_build
    
    echo ""
    print_header "Installation Complete"
    echo "You can now build the Pentathlon benchmark with RDMA support:"
    echo ""
    echo "  cd benchmarks"
    echo "  mkdir build && cd build"
    echo "  cmake -DPENTATHLON_LINK_COMPUTE=ON .."
    echo "  make benchmark-optimized"
    echo ""
    echo "Or use the build script:"
    echo "  ./build_targets.sh benchmark-optimized"
}

# Show help
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Install RDMA dependencies for Pentathlon benchmark"
    echo ""
    echo "Options:"
    echo "  -h, --help    Show this help message"
    echo "  --test-only   Only run verification and test, don't install"
    echo ""
    echo "Supported operating systems:"
    echo "  - Ubuntu/Debian"
    echo "  - CentOS/RHEL/Rocky/AlmaLinux"
    echo "  - Fedora"
    echo "  - Arch Linux/Manjaro"
}

# Parse arguments
if [[ $# -gt 0 ]]; then
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --test-only)
            detect_os
            verify_installation
            test_build
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
fi

# Run main function
main
