# New Pentathlon Benchmark Build Targets

This document describes the new make targets added to the Pentathlon benchmark CMake configuration, specifically the `benchmark-optimized` and `lru` targets with their specific library linking requirements.

## Overview

Two new specialized build targets have been added to provide optimized configurations:

1. **`benchmark-optimized`** - Links with `libskiplist_optimized.a` + `libdm_compiler_rt_compute.a`
2. **`lru`** - Links with `libskiplist_optimized.a` + `lru-cache`

## New Make Targets

### `benchmark-optimized`

**Purpose**: High-performance benchmark with optimized skiplist and compute runtime

**Executable**: `pentathlon-bm-local-optimized`

**Libraries Linked**:
- `libskiplist_optimized.a` - Optimized skiplist from MLIR/LLVM pipeline
- `libdm_compiler_rt_compute.a` - Runtime/compute module with RDMA support
- Base libraries (generators, workload)

**Usage**:
```bash
make benchmark-optimized
```

**Requirements**:
- Optimized skiplist assembly must exist at `scripts/output/skiplist.s`
- Runtime/compute module must be available
- RDMA libraries (libibverbs, librdmacm)

### `lru`

**Purpose**: LRU cache benchmark with optimized skiplist

**Executable**: `pentathlon-bm-local-lru`

**Libraries Linked**:
- `libskiplist_optimized.a` - Optimized skiplist from MLIR/LLVM pipeline
- `lru-cache` - LRU cache manager library
- Base libraries (generators, workload)

**Usage**:
```bash
make lru
```

**Requirements**:
- Optimized skiplist assembly must exist at `scripts/output/skiplist.s`
- Runtime/lru module must be available
- RDMA libraries (libibverbs, librdmacm)

## Build Process

### Prerequisites

1. **Generate Optimized Skiplist**:
   ```bash
   cd scripts
   ./compile.sh
   ```
   This creates `scripts/output/skiplist.s`

2. **Build Runtime Libraries**:
   ```bash
   ./prepare_runtime_libs.sh
   ```
   This builds and copies libraries to `runtime/build/`:
   - `libdm_compiler_rt_compute.a`
   - `liblru-cache.a`

3. **Enable Required Components**:
   ```bash
   cmake -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=ON ..
   ```

### Building

#### Using Make Targets
```bash
# Build optimized benchmark
make benchmark-optimized

# Build LRU benchmark
make lru

# Build both
make benchmark-optimized lru
```

#### Using Build Scripts
```bash
# Linux/macOS
./build_targets.sh benchmark-optimized
./build_targets.sh lru

# Windows
build_targets.bat benchmark-optimized
build_targets.bat lru
```

#### Manual CMake Configuration
```bash
# Configure for optimized benchmark
cmake -DPENTATHLON_LINK_SKIPLIST=ON \
      -DPENTATHLON_LINK_COMPUTE=ON \
      -DPENTATHLON_LINK_LRU=OFF ..
make benchmark-optimized

# Configure for LRU benchmark
cmake -DPENTATHLON_LINK_SKIPLIST=ON \
      -DPENTATHLON_LINK_COMPUTE=OFF \
      -DPENTATHLON_LINK_LRU=ON ..
make lru
```

## Output Files

### Directory Structure
```
benchmarks/build/
├── pentathlon-bm-local                 # Standard benchmark
├── pentathlon-bm-local-optimized       # Optimized benchmark
├── pentathlon-bm-local-lru             # LRU benchmark
├── libskiplist_optimized.a             # Optimized skiplist library
├── libdm_compiler_rt_compute.a         # Compute runtime library
└── liblru-cache.a                      # LRU cache library
```

### Executable Comparison

| Executable | Skiplist | Compute | LRU | Description |
|------------|----------|---------|-----|-------------|
| `pentathlon-bm-local` | Regular/Optimized | Optional | Optional | Configurable benchmark |
| `pentathlon-bm-local-optimized` | **Optimized** | **Yes** | No | High-performance benchmark |
| `pentathlon-bm-local-lru` | **Optimized** | No | **Yes** | LRU cache benchmark |

## Library Details

### `libskiplist_optimized.a`

**Source**: Generated from `scripts/output/skiplist.s`
**Features**:
- MLIR/LLVM optimized assembly
- Custom passes for disaggregated memory
- No atomic operations (mutex-based synchronization)
- Enhanced performance for remote memory access

**Generation**:
```bash
cd scripts
./compile.sh  # Creates skiplist.s
# CMake automatically creates libskiplist_optimized.a from assembly
```

### `libdm_compiler_rt_compute.a`

**Source**: `runtime/compute` module
**Features**:
- Disaggregated memory management
- RDMA support for remote memory access
- Thread-safe operations
- Address dependency tracking

**Dependencies**:
- libibverbs-dev
- librdmacm-dev
- pthread

### `lru-cache`

**Source**: `runtime/lru` module
**Features**:
- LRU eviction policy with O(1) operations
- RDMA integration for remote memory
- Comprehensive statistics and monitoring
- Thread-safe without atomic operations

**Dependencies**:
- libibverbs-dev
- librdmacm-dev
- pthread

## Configuration Examples

### Development Setup
```bash
# Build all variants for testing
make benchmark-optimized
make lru
make benchmark-all

# Compare performance
./pentathlon-bm-local-optimized [args]
./pentathlon-bm-local-lru [args]
./pentathlon-bm-local [args]
```

### Production Deployment
```bash
# Optimized build for performance testing
cmake -DCMAKE_BUILD_TYPE=Release ..
make benchmark-optimized

# LRU build for cache evaluation
cmake -DCMAKE_BUILD_TYPE=Release ..
make lru
```

### Debug Build
```bash
# Debug optimized benchmark
cmake -DCMAKE_BUILD_TYPE=Debug ..
make benchmark-optimized

# Run with debugger
gdb ./pentathlon-bm-local-optimized
```

## Troubleshooting

### Common Issues

1. **Optimized skiplist not found**
   ```
   Error: libskiplist_optimized.a not available
   ```
   **Solution**: Run `cd scripts && ./compile.sh` to generate optimized skiplist

2. **Compute runtime not available**
   ```
   Error: Pre-built compute library not found at: ../runtime/build/libdm_compiler_rt_compute.a
   ```
   **Solution**: Build runtime libraries: `./prepare_runtime_libs.sh --compute`

3. **LRU cache not available**
   ```
   Error: Pre-built LRU library not found at: ../runtime/build/liblru-cache.a
   ```
   **Solution**: Build runtime libraries: `./prepare_runtime_libs.sh --lru`

4. **All runtime libraries missing**
   ```
   Error: Both compute and LRU libraries not found
   ```
   **Solution**: Build all runtime libraries: `./prepare_runtime_libs.sh`

### Fallback Behavior

- If `libskiplist_optimized.a` is not available, the targets fall back to regular skiplist
- Warning messages indicate when fallbacks occur
- Build continues with available components

### Verification

```bash
# Check linked libraries
ldd pentathlon-bm-local-optimized
ldd pentathlon-bm-local-lru

# Check file sizes
ls -la pentathlon-bm-local*

# Test executables
./pentathlon-bm-local-optimized --help
./pentathlon-bm-local-lru --help
```

## Performance Expectations

### `benchmark-optimized`
- **Best overall performance** due to MLIR/LLVM optimizations
- **RDMA acceleration** for remote memory operations
- **Optimized memory access patterns** for disaggregated systems

### `lru`
- **Efficient caching** with O(1) LRU operations
- **Reduced remote memory access** through intelligent caching
- **Good for memory-intensive workloads** with locality

## Integration

### CI/CD Pipeline
```yaml
# Example GitHub Actions
- name: Build optimized targets
  run: |
    cd scripts && ./compile.sh
    cd ..
    mkdir build && cd build
    cmake -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=ON ..
    make benchmark-optimized lru
```

### Custom Applications
```cmake
# Use optimized benchmark in other projects
add_subdirectory(benchmarks)
target_link_libraries(my_app pentathlon-bm-local-optimized)
```

## Summary

The new `benchmark-optimized` and `lru` targets provide specialized configurations for high-performance benchmarking and cache evaluation, leveraging optimized skiplist implementations and advanced runtime components for disaggregated memory systems.
