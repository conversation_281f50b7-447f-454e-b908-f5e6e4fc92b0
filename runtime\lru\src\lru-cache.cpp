#include "lru-cache.hpp"
#include <algorithm>
#include <cstring>
#include <iostream>
#include <stdexcept>
#include <sys/mman.h>
#include <unistd.h>

#include "../../common/rdma-ops.h"
#include "../../common/parse.h"

// Global cache instance
std::unique_ptr<LRUCache> g_lru_cache = nullptr;

// Helper functions implementation
uint64_t get_timestamp_ns() {
    auto now = std::chrono::steady_clock::now();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
}

size_t align_size(size_t size, size_t alignment) {
    return (size + alignment - 1) & ~(alignment - 1);
}

bool is_power_of_two(size_t value) {
    return value != 0 && (value & (value - 1)) == 0;
}

// LRUCacheEntry implementation
LRUCacheEntry::LRUCacheEntry(GlobalAddr addr, void* local_addr, size_t size, uint8_t type_id)
    : addr(addr), local_addr(local_addr), size(size), access_count(1), 
      last_access(std::chrono::steady_clock::now()), dirty(false), pinned(false), type_id(type_id) {
}

LRUCacheEntry::~LRUCacheEntry() {
    if (local_addr) {
        free(local_addr);
        local_addr = nullptr;
    }
}

void LRUCacheEntry::touch() {
    last_access = std::chrono::steady_clock::now();
    access_count++;
}

bool LRUCacheEntry::is_expired(uint64_t max_age_ms) const {
    auto now = std::chrono::steady_clock::now();
    auto age = std::chrono::duration_cast<std::chrono::milliseconds>(now - last_access);
    return age.count() > static_cast<int64_t>(max_age_ms);
}

// RDMAManager implementation
RDMAManager::RDMAManager(const char* memory_server_addr) 
    : conn(nullptr), rdma_buffer(nullptr), rdma_mr(nullptr), buffer_size(4096) {
    
    // Parse server address
    struct sockaddr_storage addr;
    if (c_parse_addr(const_cast<char*>(memory_server_addr), &addr, 12345) < 0) {
        throw std::runtime_error("Failed to parse memory server address");
    }
    
    // Create RDMA event channel
    struct rdma_event_channel* events = rdma_create_event_channel();
    if (!events) {
        throw std::runtime_error("Failed to create RDMA event channel");
    }
    
    // Create RDMA CM ID
    struct rdma_cm_id* id = nullptr;
    if (rdma_create_id(events, &id, nullptr, RDMA_PS_TCP) < 0) {
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Failed to create RDMA CM ID");
    }
    
    // Resolve address
    if (rdma_resolve_addr(id, nullptr, (struct sockaddr*)&addr, 2000) < 0) {
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Failed to resolve RDMA address");
    }
    
    // Wait for address resolution
    if (expect_event(events, RDMA_CM_EVENT_ADDR_RESOLVED) < 0) {
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Address resolution failed");
    }
    
    // Resolve route
    if (rdma_resolve_route(id, 2000) < 0) {
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Failed to resolve RDMA route");
    }
    
    // Wait for route resolution
    if (expect_event(events, RDMA_CM_EVENT_ROUTE_RESOLVED) < 0) {
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Route resolution failed");
    }
    
    // Create connection
    conn = rdma_conn_create(id, false);
    if (!conn) {
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Failed to create RDMA connection");
    }
    
    // Connect
    struct compute_info local_info = {};
    if (rdma_connect(id, nullptr) < 0) {
        // Cleanup connection
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Failed to connect via RDMA");
    }
    
    // Wait for connection establishment
    struct rdma_conn_param conn_param;
    if (expect_established(events, &conn_param) < 0) {
        rdma_destroy_id(id);
        rdma_destroy_event_channel(events);
        throw std::runtime_error("Connection establishment failed");
    }
    
    // Get memory info from connection parameters
    if (conn_param.private_data_len >= sizeof(struct memory_info)) {
        memcpy(&mem_info, conn_param.private_data, sizeof(struct memory_info));
    } else {
        // Default memory info if not provided
        mem_info.addr = 0x100000000ULL;  // 4GB
        mem_info.page_size = 4096;
        mem_info.page_count = 1024 * 1024;  // 4GB / 4KB
        mem_info.rkey = 0;
    }
    
    // Allocate and register RDMA buffer
    rdma_buffer = aligned_alloc(4096, buffer_size);
    if (!rdma_buffer) {
        throw std::runtime_error("Failed to allocate RDMA buffer");
    }
    
    rdma_mr = ibv_reg_mr(conn->pd, rdma_buffer, buffer_size,
                         IBV_ACCESS_LOCAL_WRITE | IBV_ACCESS_REMOTE_READ | IBV_ACCESS_REMOTE_WRITE);
    if (!rdma_mr) {
        free(rdma_buffer);
        throw std::runtime_error("Failed to register RDMA memory region");
    }
}

RDMAManager::~RDMAManager() {
    if (rdma_mr) {
        ibv_dereg_mr(rdma_mr);
    }
    if (rdma_buffer) {
        free(rdma_buffer);
    }
    if (conn) {
        // Cleanup RDMA connection
        if (conn->id) {
            rdma_disconnect(conn->id);
            rdma_destroy_id(conn->id);
        }
        free(conn);
    }
}

bool RDMAManager::read(GlobalAddr from, void* to, size_t size) {
    if (size > buffer_size) {
        return false;  // Size too large for buffer
    }
    
    std::lock_guard<std::mutex> lock(rdma_mutex);
    
    // Prepare scatter-gather entry
    struct ibv_sge sge = {
        .addr = reinterpret_cast<uint64_t>(rdma_buffer),
        .length = static_cast<uint32_t>(size),
        .lkey = rdma_mr->lkey
    };
    
    // Prepare work request
    struct ibv_send_wr wr = {
        .wr_id = 0,
        .next = nullptr,
        .sg_list = &sge,
        .num_sge = 1,
        .opcode = IBV_WR_RDMA_READ,
        .send_flags = IBV_SEND_SIGNALED,
        .wr = {
            .rdma = {
                .remote_addr = from.offset,
                .rkey = mem_info.rkey
            }
        }
    };
    
    // Post send request
    struct ibv_send_wr* bad_wr = nullptr;
    if (ibv_post_send(conn->id->qp, &wr, &bad_wr) != 0) {
        return false;
    }
    
    // Wait for completion
    struct ibv_wc wc;
    int num_comp = 0;
    do {
        num_comp = ibv_poll_cq(conn->send_cq, 1, &wc);
    } while (num_comp == 0);
    
    if (num_comp < 0 || wc.status != IBV_WC_SUCCESS) {
        return false;
    }
    
    // Copy data to destination
    memcpy(to, rdma_buffer, size);
    return true;
}

bool RDMAManager::write(const void* from, GlobalAddr to, size_t size) {
    if (size > buffer_size) {
        return false;  // Size too large for buffer
    }
    
    std::lock_guard<std::mutex> lock(rdma_mutex);
    
    // Copy data to RDMA buffer
    memcpy(rdma_buffer, from, size);
    
    // Prepare scatter-gather entry
    struct ibv_sge sge = {
        .addr = reinterpret_cast<uint64_t>(rdma_buffer),
        .length = static_cast<uint32_t>(size),
        .lkey = rdma_mr->lkey
    };
    
    // Prepare work request
    struct ibv_send_wr wr = {
        .wr_id = 0,
        .next = nullptr,
        .sg_list = &sge,
        .num_sge = 1,
        .opcode = IBV_WR_RDMA_WRITE,
        .send_flags = IBV_SEND_SIGNALED,
        .wr = {
            .rdma = {
                .remote_addr = to.offset,
                .rkey = mem_info.rkey
            }
        }
    };
    
    // Post send request
    struct ibv_send_wr* bad_wr = nullptr;
    if (ibv_post_send(conn->id->qp, &wr, &bad_wr) != 0) {
        return false;
    }
    
    // Wait for completion
    struct ibv_wc wc;
    int num_comp = 0;
    do {
        num_comp = ibv_poll_cq(conn->send_cq, 1, &wc);
    } while (num_comp == 0);
    
    return (num_comp > 0 && wc.status == IBV_WC_SUCCESS);
}

bool RDMAManager::read_async(GlobalAddr from, void* to, size_t size) {
    // For now, implement as synchronous read
    // In a full implementation, this would use asynchronous RDMA operations
    return read(from, to, size);
}

bool RDMAManager::write_async(const void* from, GlobalAddr to, size_t size) {
    // For now, implement as synchronous write
    // In a full implementation, this would use asynchronous RDMA operations
    return write(from, to, size);
}

// LRUCache implementation
LRUCache::LRUCache(const char* memory_server_addr)
    : current_size(0), current_entries(0), tracing_enabled(false),
      eviction_callback(nullptr), eviction_callback_data(nullptr),
      miss_callback(nullptr), miss_callback_data(nullptr) {

    // Initialize RDMA manager
    rdma_manager = std::make_unique<RDMAManager>(memory_server_addr);

    // Initialize default configuration
    config.max_size = 1024 * 1024 * 1024;  // 1GB default
    config.max_entries = 10000000;           // 10M entries default
    config.eviction_policy = LRU_POLICY_STRICT_LRU;
    config.write_policy = LRU_WRITE_BACK;
    config.enable_prefetch = false;
    config.prefetch_distance = 4096;
    config.enable_compression = false;
    config.compression_ratio = 0.5;

    // Initialize statistics
    memset(&stats, 0, sizeof(stats));
    start_time = std::chrono::steady_clock::now();

    // Reserve space for trace buffer
    trace_buffer.reserve(10000);
}

LRUCache::~LRUCache() {
    clear();
}

bool LRUCache::set_config(const lru_cache_config_t& new_config) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    // Validate configuration
    if (new_config.max_size == 0 || new_config.max_entries == 0) {
        return false;
    }

    config = new_config;

    // Evict entries if current size exceeds new limit
    while (current_size > config.max_size || current_entries > config.max_entries) {
        if (!evict_lru_entry()) {
            break;
        }
    }

    return true;
}

void* LRUCache::get(GlobalAddr addr, size_t size) {
    auto start_time = get_timestamp_ns();
    std::lock_guard<std::mutex> lock(cache_mutex);

    // Check if entry exists in cache
    auto entry_it = cache_entries.find(addr);
    if (entry_it != cache_entries.end()) {
        // Cache hit
        auto& entry = entry_it->second;
        std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);

        entry->touch();
        move_to_front(addr);

        auto end_time = get_timestamp_ns();
        update_statistics(true, size, end_time - start_time);

        if (tracing_enabled) {
            add_trace_entry(addr, size, true, end_time - start_time);
        }

        return entry->local_addr;
    }

    // Cache miss - need to fetch from remote memory
    if (miss_callback) {
        miss_callback(reinterpret_cast<void*>(addr.val), nullptr, size, miss_callback_data);
    }

    // Check if we need to evict entries to make space
    while (is_cache_full(size) || current_entries >= config.max_entries) {
        if (!evict_lru_entry()) {
            // Cannot evict any more entries
            auto end_time = get_timestamp_ns();
            update_statistics(false, size, end_time - start_time);
            return nullptr;
        }
    }

    // Allocate local memory for the data
    void* local_addr = allocate_local_memory(size);
    if (!local_addr) {
        auto end_time = get_timestamp_ns();
        update_statistics(false, size, end_time - start_time);
        return nullptr;
    }

    // Fetch data from remote memory via RDMA
    if (!rdma_manager->read(addr, local_addr, size)) {
        free_local_memory(local_addr);
        auto end_time = get_timestamp_ns();
        update_statistics(false, size, end_time - start_time);
        return nullptr;
    }

    // Create cache entry
    auto entry = std::make_unique<LRUCacheEntry>(addr, local_addr, size, addr.typeID);

    // Add to cache structures
    cache_entries[addr] = std::move(entry);
    lru_list.push_front(addr);
    lru_map[addr] = lru_list.begin();
    current_size += size;
    current_entries++;

    auto end_time = get_timestamp_ns();
    update_statistics(false, size, end_time - start_time);

    if (tracing_enabled) {
        add_trace_entry(addr, size, false, end_time - start_time);
    }

    return local_addr;
}

bool LRUCache::put(GlobalAddr addr, const void* data, size_t size) {
    if (!data) {
        return false;
    }

    std::lock_guard<std::mutex> lock(cache_mutex);

    // Check if entry already exists
    auto entry_it = cache_entries.find(addr);
    if (entry_it != cache_entries.end()) {
        // Update existing entry
        auto& entry = entry_it->second;
        std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);

        if (entry->size != size) {
            // Size mismatch - need to reallocate
            void* new_addr = realloc(entry->local_addr, size);
            if (!new_addr) {
                return false;
            }
            current_size = current_size - entry->size + size;
            entry->local_addr = new_addr;
            entry->size = size;
        }

        memcpy(entry->local_addr, data, size);
        entry->dirty = true;
        entry->touch();
        move_to_front(addr);

        // Write through if configured
        if (config.write_policy == LRU_WRITE_THROUGH) {
            rdma_manager->write(data, addr, size);
            entry->dirty = false;
        }

        return true;
    }

    // New entry - check if we need to evict
    while (is_cache_full(size) || current_entries >= config.max_entries) {
        if (!evict_lru_entry()) {
            return false;
        }
    }

    // Allocate memory and create entry
    void* local_addr = allocate_local_memory(size);
    if (!local_addr) {
        return false;
    }

    memcpy(local_addr, data, size);
    auto entry = std::make_unique<LRUCacheEntry>(addr, local_addr, size, addr.typeID);
    entry->dirty = true;

    // Write through if configured
    if (config.write_policy == LRU_WRITE_THROUGH) {
        if (rdma_manager->write(data, addr, size)) {
            entry->dirty = false;
        }
    }

    // Add to cache structures
    cache_entries[addr] = std::move(entry);
    lru_list.push_front(addr);
    lru_map[addr] = lru_list.begin();
    current_size += size;
    current_entries++;

    return true;
}

bool LRUCache::remove(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    auto entry_it = cache_entries.find(addr);
    if (entry_it == cache_entries.end()) {
        return false;
    }

    auto& entry = entry_it->second;

    // Write back if dirty
    if (entry->dirty) {
        std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);
        if (!rdma_manager->write(entry->local_addr, addr, entry->size)) {
            std::cerr << "Warning: Failed to write back dirty entry during removal" << std::endl;
        }
        stats.writebacks++;
    }

    // Remove from cache structures
    current_size -= entry->size;
    current_entries--;
    remove_from_lru(addr);
    cache_entries.erase(entry_it);

    return true;
}

void LRUCache::clear() {
    std::lock_guard<std::mutex> lock(cache_mutex);

    // Write back all dirty entries
    for (auto& pair : cache_entries) {
        auto& entry = pair.second;
        if (entry->dirty) {
            std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);
            if (!rdma_manager->write(entry->local_addr, pair.first, entry->size)) {
                std::cerr << "Warning: Failed to write back dirty entry during clear" << std::endl;
            }
            stats.writebacks++;
        }
    }

    // Clear all data structures
    cache_entries.clear();
    lru_list.clear();
    lru_map.clear();
    current_size = 0;
    current_entries = 0;
}

bool LRUCache::contains(GlobalAddr addr) const {
    std::lock_guard<std::mutex> lock(cache_mutex);
    return cache_entries.find(addr) != cache_entries.end();
}

void LRUCache::touch(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    auto entry_it = cache_entries.find(addr);
    if (entry_it != cache_entries.end()) {
        std::lock_guard<std::mutex> entry_lock(entry_it->second->entry_mutex);
        entry_it->second->touch();
        move_to_front(addr);
    }
}

void LRUCache::mark_dirty(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    auto entry_it = cache_entries.find(addr);
    if (entry_it != cache_entries.end()) {
        std::lock_guard<std::mutex> entry_lock(entry_it->second->entry_mutex);
        entry_it->second->dirty = true;
    }
}

void LRUCache::flush(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    auto entry_it = cache_entries.find(addr);
    if (entry_it == cache_entries.end()) {
        return;
    }

    auto& entry = entry_it->second;
    std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);

    if (entry->dirty) {
        if (rdma_manager->write(entry->local_addr, addr, entry->size)) {
            entry->dirty = false;
            stats.writebacks++;
        }
    }
}

void LRUCache::flush_all() {
    std::lock_guard<std::mutex> lock(cache_mutex);

    for (auto& pair : cache_entries) {
        auto& entry = pair.second;
        std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);

        if (entry->dirty) {
            if (rdma_manager->write(entry->local_addr, pair.first, entry->size)) {
                entry->dirty = false;
                stats.writebacks++;
            }
        }
    }
}

// Private helper methods
void LRUCache::move_to_front(GlobalAddr addr) {
    auto lru_it = lru_map.find(addr);
    if (lru_it != lru_map.end()) {
        // Remove from current position
        lru_list.erase(lru_it->second);
        // Add to front
        lru_list.push_front(addr);
        // Update map
        lru_map[addr] = lru_list.begin();
    }
}

void LRUCache::remove_from_lru(GlobalAddr addr) {
    auto lru_it = lru_map.find(addr);
    if (lru_it != lru_map.end()) {
        lru_list.erase(lru_it->second);
        lru_map.erase(lru_it);
    }
}

bool LRUCache::evict_lru_entry() {
    if (lru_list.empty()) {
        return false;
    }

    // Get least recently used entry (at the back)
    GlobalAddr lru_addr = lru_list.back();
    auto entry_it = cache_entries.find(lru_addr);

    if (entry_it == cache_entries.end()) {
        // Inconsistent state - remove from LRU list and try again
        lru_list.pop_back();
        lru_map.erase(lru_addr);
        return !lru_list.empty();
    }

    auto& entry = entry_it->second;

    // Don't evict pinned entries
    if (entry->pinned) {
        // Move to front and try next entry
        move_to_front(lru_addr);
        return evict_lru_entry();
    }

    // Call eviction callback if set
    if (eviction_callback) {
        eviction_callback(reinterpret_cast<void*>(lru_addr.val),
                         entry->local_addr, entry->size, eviction_callback_data);
    }

    // Write back if dirty
    if (entry->dirty) {
        std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);
        if (!rdma_manager->write(entry->local_addr, lru_addr, entry->size)) {
            std::cerr << "Warning: Failed to write back evicted entry" << std::endl;
        }
        stats.writebacks++;
    }

    // Remove from all data structures
    current_size -= entry->size;
    current_entries--;
    lru_list.pop_back();
    lru_map.erase(lru_addr);
    cache_entries.erase(entry_it);

    stats.evictions++;
    return true;
}

void LRUCache::update_statistics(bool hit, size_t size, uint64_t latency_ns) {
    std::lock_guard<std::mutex> lock(stats_mutex);

    stats.total_requests++;
    if (hit) {
        stats.cache_hits++;
    } else {
        stats.cache_misses++;
        stats.bytes_read += size;
        stats.rdma_reads++;
    }

    stats.hit_rate = static_cast<double>(stats.cache_hits) / stats.total_requests;
    stats.miss_rate = 1.0 - stats.hit_rate;

    // Update average access time
    stats.avg_access_time = (stats.avg_access_time * (stats.total_requests - 1) +
                            latency_ns / 1000.0) / stats.total_requests;

    if (!hit) {
        stats.avg_miss_penalty = (stats.avg_miss_penalty * (stats.cache_misses - 1) +
                                 latency_ns / 1000.0) / stats.cache_misses;
    }
}

bool LRUCache::is_cache_full(size_t additional_size) const {
    return current_size + additional_size > config.max_size;
}

void* LRUCache::allocate_local_memory(size_t size) {
    // Align to cache line boundary for better performance
    size_t aligned_size = align_size(size, 64);
    return aligned_alloc(64, aligned_size);
}

void LRUCache::free_local_memory(void* ptr) {
    if (ptr) {
        free(ptr);
    }
}

void LRUCache::add_trace_entry(GlobalAddr addr, size_t size, bool hit, uint64_t latency_ns) {
    if (!tracing_enabled) return;

    std::lock_guard<std::mutex> lock(trace_mutex);

    if (trace_buffer.size() < trace_buffer.capacity()) {
        lru_cache_trace_entry_t entry = {
            .timestamp = get_timestamp_ns(),
            .gaddr = reinterpret_cast<void*>(addr.val),
            .size = size,
            .hit = hit,
            .latency_ns = latency_ns
        };
        trace_buffer.push_back(entry);
    }
}

// Additional LRUCache methods implementation

void LRUCache::sync(GlobalAddr addr) {
    flush(addr);
}

void LRUCache::pin(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    auto entry_it = cache_entries.find(addr);
    if (entry_it != cache_entries.end()) {
        std::lock_guard<std::mutex> entry_lock(entry_it->second->entry_mutex);
        entry_it->second->pinned = true;
    }
}

void LRUCache::unpin(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    auto entry_it = cache_entries.find(addr);
    if (entry_it != cache_entries.end()) {
        std::lock_guard<std::mutex> entry_lock(entry_it->second->entry_mutex);
        entry_it->second->pinned = false;
    }
}

int LRUCache::get_batch(lru_cache_request_t* requests, size_t count) {
    if (!requests || count == 0) {
        return -1;
    }

    int successful = 0;
    for (size_t i = 0; i < count; i++) {
        GlobalAddr addr = GlobalAddr::fromPointer(requests[i].gaddr);
        requests[i].local_addr = get(addr, requests[i].size);
        requests[i].hit = (requests[i].local_addr != nullptr);
        if (requests[i].hit) {
            successful++;
        }
    }

    return successful;
}

int LRUCache::put_batch(const lru_cache_request_t* requests, size_t count) {
    if (!requests || count == 0) {
        return -1;
    }

    int successful = 0;
    for (size_t i = 0; i < count; i++) {
        if (requests[i].local_addr) {
            GlobalAddr addr = GlobalAddr::fromPointer(requests[i].gaddr);
            if (put(addr, requests[i].local_addr, requests[i].size)) {
                successful++;
            }
        }
    }

    return successful;
}

bool LRUCache::evict_specific(GlobalAddr addr) {
    std::lock_guard<std::mutex> lock(cache_mutex);
    return evict_entry(addr);
}

bool LRUCache::evict_entry(GlobalAddr addr) {
    auto entry_it = cache_entries.find(addr);
    if (entry_it == cache_entries.end()) {
        return false;
    }

    auto& entry = entry_it->second;

    // Don't evict pinned entries
    if (entry->pinned) {
        return false;
    }

    // Write back if dirty
    if (entry->dirty) {
        std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);
        if (!rdma_manager->write(entry->local_addr, addr, entry->size)) {
            std::cerr << "Warning: Failed to write back evicted entry" << std::endl;
        }
        stats.writebacks++;
    }

    // Remove from all data structures
    current_size -= entry->size;
    current_entries--;
    remove_from_lru(addr);
    cache_entries.erase(entry_it);

    stats.evictions++;
    return true;
}

size_t LRUCache::evict_n_entries(size_t n) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    size_t evicted = 0;
    for (size_t i = 0; i < n && !lru_list.empty(); i++) {
        if (evict_lru_entry()) {
            evicted++;
        } else {
            break;
        }
    }

    return evicted;
}

void LRUCache::evict_by_type(uint8_t type_id) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    std::vector<GlobalAddr> to_evict;
    for (const auto& pair : cache_entries) {
        if (pair.first.typeID == type_id) {
            to_evict.push_back(pair.first);
        }
    }

    for (const auto& addr : to_evict) {
        evict_entry(addr);
    }
}

void LRUCache::evict_old_entries(uint64_t max_age_ms) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    std::vector<GlobalAddr> to_evict;
    for (const auto& pair : cache_entries) {
        if (pair.second->is_expired(max_age_ms)) {
            to_evict.push_back(pair.first);
        }
    }

    for (const auto& addr : to_evict) {
        evict_entry(addr);
    }
}

void LRUCache::prefetch(GlobalAddr addr, size_t size) {
    // Simply call get() which will fetch the data if not cached
    get(addr, size);
}

void LRUCache::prefetch_sequential(GlobalAddr start_addr, size_t total_size, size_t block_size) {
    for (size_t offset = 0; offset < total_size; offset += block_size) {
        GlobalAddr addr = start_addr;
        addr.offset += offset;
        size_t current_block_size = std::min(block_size, total_size - offset);
        prefetch(addr, current_block_size);
    }
}

void LRUCache::prefetch_pattern(const std::vector<GlobalAddr>& addrs) {
    for (const auto& addr : addrs) {
        // Use a default size for prefetching
        prefetch(addr, 4096);
    }
}

void LRUCache::get_stats(lru_cache_stats_t& stats_out) const {
    std::lock_guard<std::mutex> stats_lock(stats_mutex);
    std::lock_guard<std::mutex> cache_lock(cache_mutex);

    stats_out = stats;
    stats_out.current_size = current_size;
    stats_out.entry_count = current_entries;
    stats_out.max_size = config.max_size;
    stats_out.max_entries = config.max_entries;
}

void LRUCache::reset_stats() {
    std::lock_guard<std::mutex> lock(stats_mutex);
    memset(&stats, 0, sizeof(stats));
    start_time = std::chrono::steady_clock::now();
}

void LRUCache::print_stats() const {
    lru_cache_stats_t current_stats;
    get_stats(current_stats);

    std::cout << "=== LRU Cache Statistics ===" << std::endl;
    std::cout << "Current size: " << current_stats.current_size << " bytes" << std::endl;
    std::cout << "Max size: " << current_stats.max_size << " bytes" << std::endl;
    std::cout << "Entry count: " << current_stats.entry_count << std::endl;
    std::cout << "Max entries: " << current_stats.max_entries << std::endl;
    std::cout << "Total requests: " << current_stats.total_requests << std::endl;
    std::cout << "Cache hits: " << current_stats.cache_hits << std::endl;
    std::cout << "Cache misses: " << current_stats.cache_misses << std::endl;
    std::cout << "Hit rate: " << (current_stats.hit_rate * 100.0) << "%" << std::endl;
    std::cout << "Evictions: " << current_stats.evictions << std::endl;
    std::cout << "Write-backs: " << current_stats.writebacks << std::endl;
    std::cout << "===========================" << std::endl;
}

void LRUCache::print_detailed_stats() const {
    print_stats();

    lru_cache_stats_t current_stats;
    get_stats(current_stats);

    std::cout << "=== Detailed Statistics ===" << std::endl;
    std::cout << "Miss rate: " << (current_stats.miss_rate * 100.0) << "%" << std::endl;
    std::cout << "Eviction rate: " << (current_stats.eviction_rate * 100.0) << "%" << std::endl;
    std::cout << "Bytes read: " << current_stats.bytes_read << std::endl;
    std::cout << "Bytes written: " << current_stats.bytes_written << std::endl;
    std::cout << "RDMA reads: " << current_stats.rdma_reads << std::endl;
    std::cout << "RDMA writes: " << current_stats.rdma_writes << std::endl;
    std::cout << "Avg access time: " << current_stats.avg_access_time << " μs" << std::endl;
    std::cout << "Avg miss penalty: " << current_stats.avg_miss_penalty << " μs" << std::endl;
    std::cout << "==========================" << std::endl;
}

// Cache coherence and debugging methods
void LRUCache::invalidate_range(GlobalAddr start_addr, size_t size) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    std::vector<GlobalAddr> to_invalidate;
    for (const auto& pair : cache_entries) {
        GlobalAddr addr = pair.first;
        if (addr.offset >= start_addr.offset &&
            addr.offset < start_addr.offset + size) {
            to_invalidate.push_back(addr);
        }
    }

    for (const auto& addr : to_invalidate) {
        remove(addr);
    }
}

void LRUCache::flush_range(GlobalAddr start_addr, size_t size) {
    std::lock_guard<std::mutex> lock(cache_mutex);

    for (const auto& pair : cache_entries) {
        GlobalAddr addr = pair.first;
        if (addr.offset >= start_addr.offset &&
            addr.offset < start_addr.offset + size) {
            auto& entry = pair.second;
            std::lock_guard<std::mutex> entry_lock(entry->entry_mutex);

            if (entry->dirty) {
                if (rdma_manager->write(entry->local_addr, addr, entry->size)) {
                    entry->dirty = false;
                    stats.writebacks++;
                }
            }
        }
    }
}

void LRUCache::barrier() {
    // Memory barrier - ensure all pending RDMA operations complete
    flush_all();
}

void LRUCache::set_eviction_callback(lru_cache_callback_t callback, void* user_data) {
    eviction_callback = callback;
    eviction_callback_data = user_data;
}

void LRUCache::set_miss_callback(lru_cache_callback_t callback, void* user_data) {
    miss_callback = callback;
    miss_callback_data = user_data;
}

void LRUCache::dump_entries() const {
    std::lock_guard<std::mutex> lock(cache_mutex);

    std::cout << "=== Cache Entries Dump ===" << std::endl;
    std::cout << "Total entries: " << cache_entries.size() << std::endl;

    int count = 0;
    for (const auto& pair : cache_entries) {
        const auto& addr = pair.first;
        const auto& entry = pair.second;

        std::cout << "Entry " << count++ << ":" << std::endl;
        std::cout << "  Address: 0x" << std::hex << addr.val << std::dec << std::endl;
        std::cout << "  Type ID: " << static_cast<int>(addr.typeID) << std::endl;
        std::cout << "  Size: " << entry->size << " bytes" << std::endl;
        std::cout << "  Access count: " << entry->access_count << std::endl;
        std::cout << "  Dirty: " << (entry->dirty ? "yes" : "no") << std::endl;
        std::cout << "  Pinned: " << (entry->pinned ? "yes" : "no") << std::endl;

        if (count >= 20) {  // Limit output
            std::cout << "  ... (showing first 20 entries)" << std::endl;
            break;
        }
    }

    std::cout << "=========================" << std::endl;
}

bool LRUCache::validate_consistency() const {
    std::lock_guard<std::mutex> lock(cache_mutex);

    // Check that LRU list and map are consistent
    if (lru_list.size() != lru_map.size()) {
        std::cerr << "LRU list and map size mismatch" << std::endl;
        return false;
    }

    if (lru_list.size() != cache_entries.size()) {
        std::cerr << "LRU list and cache entries size mismatch" << std::endl;
        return false;
    }

    // Check that all entries in LRU list exist in cache
    for (const auto& addr : lru_list) {
        if (cache_entries.find(addr) == cache_entries.end()) {
            std::cerr << "LRU list contains address not in cache" << std::endl;
            return false;
        }
    }

    // Check that all cache entries exist in LRU structures
    for (const auto& pair : cache_entries) {
        if (lru_map.find(pair.first) == lru_map.end()) {
            std::cerr << "Cache entry not in LRU map" << std::endl;
            return false;
        }
    }

    return true;
}

bool LRUCache::check_integrity() const {
    return validate_consistency();
}

void LRUCache::print_lru_order() const {
    std::lock_guard<std::mutex> lock(cache_mutex);

    std::cout << "=== LRU Order (Most Recent First) ===" << std::endl;

    int count = 0;
    for (const auto& addr : lru_list) {
        auto entry_it = cache_entries.find(addr);
        if (entry_it != cache_entries.end()) {
            const auto& entry = entry_it->second;
            std::cout << count++ << ": 0x" << std::hex << addr.val << std::dec
                      << " (size: " << entry->size << ", accesses: " << entry->access_count << ")" << std::endl;

            if (count >= 10) {  // Limit output
                std::cout << "  ... (showing first 10 entries)" << std::endl;
                break;
            }
        }
    }

    std::cout << "====================================" << std::endl;
}

void LRUCache::enable_tracing(bool enable) {
    std::lock_guard<std::mutex> lock(trace_mutex);
    tracing_enabled = enable;
    if (!enable) {
        trace_buffer.clear();
    }
}

size_t LRUCache::get_trace(lru_cache_trace_entry_t* buffer, size_t buffer_size) {
    if (!buffer || buffer_size == 0) {
        return 0;
    }

    std::lock_guard<std::mutex> lock(trace_mutex);

    size_t copy_count = std::min(buffer_size, trace_buffer.size());
    for (size_t i = 0; i < copy_count; i++) {
        buffer[i] = trace_buffer[i];
    }

    return copy_count;
}

void LRUCache::clear_trace() {
    std::lock_guard<std::mutex> lock(trace_mutex);
    trace_buffer.clear();
}
