#include "addr.h"
#include "init.hpp"

void* getLocalAddr(void* ptr) {
    // Implementation of getting local address
    // This function should return the local address associated with the given global address
    GlobalAddr gaddr = GlobalAddr::fromPointer(ptr);  // Cast the pointer to GlobalAddr
    void* local_addr = nullptr;
    GlobalState::get_instance().data->cacheInsert(gaddr);
    local_addr = GlobalState::get_instance().data->getLocalAddr(gaddr);
    return local_addr;
}

bool isLocalAddr(void* ptr) {
    // Implementation of checking if the address is local
    // This function should return true if the address is local, false otherwise
    GlobalAddr gaddr = GlobalAddr::fromPointer(ptr);  // Cast the pointer to GlobalAddr
    bool is_local = (GlobalState::get_instance().data->getLocalAddr(gaddr) == nullptr);
    return is_local;
}

void addAddrDep(void* addr_u, void* addr_v) {
    // Implementation of adding address dependency
    GlobalState::get_instance().data->addAddrDep(GlobalAddr::fromPointer(addr_u), GlobalAddr::fromPointer(addr_v));
}

void acceptAddrDep(void* addr) {
    // Implementation of accepting address dependency
    GlobalState::get_instance().data->acceptAddrDep(GlobalAddr::fromPointer(addr));
}
