#include "../include/alloc.h"
#include "lru-cache.hpp"
#include <cstring>
#include <unordered_map>
#include <mutex>

// Global allocation tracking
static std::unordered_map<void*, size_t> g_allocation_sizes;
static std::unordered_map<void*, lru_mem_type_t> g_allocation_types;
static std::mutex g_alloc_mutex;
static lru_alloc_stats_t g_alloc_stats = {};
static uint64_t g_next_offset = 0x100000000ULL;  // Start at 4GB

// Helper function to create GlobalAddr from size and type
static GlobalAddr create_global_addr(size_t size, lru_mem_type_t type) {
    GlobalAddr addr;
    addr.typeID = static_cast<uint8_t>(type);
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    addr.offset = g_next_offset;
    g_next_offset += size;
    
    return addr;
}

extern "C" {

// Core allocation functions
void* disaggAlloc(size_t size) {
    return disaggAllocTyped(size, LRU_MEM_TYPE_DEFAULT);
}

void disaggFree(void* gaddr) {
    if (!gaddr) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    
    auto size_it = g_allocation_sizes.find(gaddr);
    if (size_it != g_allocation_sizes.end()) {
        g_alloc_stats.total_freed += size_it->second;
        g_alloc_stats.current_usage -= size_it->second;
        g_alloc_stats.free_count++;
        
        g_allocation_sizes.erase(size_it);
    }
    
    auto type_it = g_allocation_types.find(gaddr);
    if (type_it != g_allocation_types.end()) {
        g_allocation_types.erase(type_it);
    }
    
    // Remove from cache if present
    if (g_lru_cache) {
        GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
        g_lru_cache->remove(addr);
    }
}

// Extended allocation functions
void* disaggAllocCached(size_t size) {
    void* gaddr = disaggAlloc(size);
    if (gaddr && g_lru_cache) {
        // Immediately cache the allocation
        GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
        
        // Allocate and zero local memory
        void* local_data = calloc(1, size);
        if (local_data) {
            g_lru_cache->put(addr, local_data, size);
            free(local_data);
        }
    }
    return gaddr;
}

void* disaggAllocAligned(size_t size, size_t alignment) {
    // For simplicity, just use regular allocation
    // In a full implementation, this would ensure proper alignment
    return disaggAlloc(size);
}

void* disaggRealloc(void* gaddr, size_t new_size) {
    if (!gaddr) {
        return disaggAlloc(new_size);
    }
    
    if (new_size == 0) {
        disaggFree(gaddr);
        return nullptr;
    }
    
    size_t old_size = disaggGetSize(gaddr);
    lru_mem_type_t type = disaggGetType(gaddr);
    
    void* new_gaddr = disaggAllocTyped(new_size, type);
    if (!new_gaddr) {
        return nullptr;
    }
    
    // Copy data if both addresses are cached
    if (g_lru_cache) {
        GlobalAddr old_addr = GlobalAddr::fromPointer(gaddr);
        GlobalAddr new_addr = GlobalAddr::fromPointer(new_gaddr);
        
        void* old_data = g_lru_cache->get(old_addr, old_size);
        if (old_data) {
            size_t copy_size = (old_size < new_size) ? old_size : new_size;
            g_lru_cache->put(new_addr, old_data, copy_size);
        }
    }
    
    disaggFree(gaddr);
    return new_gaddr;
}

// Bulk allocation operations
void** disaggAllocBulk(size_t* sizes, size_t count) {
    if (!sizes || count == 0) {
        return nullptr;
    }
    
    void** gaddrs = static_cast<void**>(malloc(count * sizeof(void*)));
    if (!gaddrs) {
        return nullptr;
    }
    
    for (size_t i = 0; i < count; i++) {
        gaddrs[i] = disaggAlloc(sizes[i]);
        if (!gaddrs[i]) {
            // Cleanup on failure
            for (size_t j = 0; j < i; j++) {
                disaggFree(gaddrs[j]);
            }
            free(gaddrs);
            return nullptr;
        }
    }
    
    return gaddrs;
}

void disaggFreeBulk(void** gaddrs, size_t count) {
    if (!gaddrs) {
        return;
    }
    
    for (size_t i = 0; i < count; i++) {
        disaggFree(gaddrs[i]);
    }
    
    free(gaddrs);
}

// Memory type management
void* disaggAllocTyped(size_t size, lru_mem_type_t type) {
    if (size == 0) {
        return nullptr;
    }
    
    GlobalAddr addr = create_global_addr(size, type);
    void* gaddr = reinterpret_cast<void*>(addr.val);
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    
    // Track allocation
    g_allocation_sizes[gaddr] = size;
    g_allocation_types[gaddr] = type;
    
    // Update statistics
    g_alloc_stats.total_allocated += size;
    g_alloc_stats.current_usage += size;
    g_alloc_stats.allocation_count++;
    
    if (g_alloc_stats.current_usage > g_alloc_stats.peak_usage) {
        g_alloc_stats.peak_usage = g_alloc_stats.current_usage;
    }
    
    return gaddr;
}

void disaggFreeTyped(void* gaddr, lru_mem_type_t type) {
    // For now, just call regular free
    // In a full implementation, this might have type-specific cleanup
    disaggFree(gaddr);
}

// Memory information and statistics
size_t disaggGetSize(void* gaddr) {
    if (!gaddr) {
        return 0;
    }
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    auto it = g_allocation_sizes.find(gaddr);
    return (it != g_allocation_sizes.end()) ? it->second : 0;
}

size_t disaggGetAlignment(void* gaddr) {
    // For simplicity, return default alignment
    return sizeof(void*);
}

lru_mem_type_t disaggGetType(void* gaddr) {
    if (!gaddr) {
        return LRU_MEM_TYPE_DEFAULT;
    }
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    auto it = g_allocation_types.find(gaddr);
    return (it != g_allocation_types.end()) ? it->second : LRU_MEM_TYPE_DEFAULT;
}

// Allocation statistics
void disaggGetStats(lru_alloc_stats_t* stats) {
    if (!stats) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    *stats = g_alloc_stats;
    
    // Add cache statistics if available
    if (g_lru_cache) {
        lru_cache_stats_t cache_stats;
        g_lru_cache->get_stats(cache_stats);
        stats->cache_hits = cache_stats.cache_hits;
        stats->cache_misses = cache_stats.cache_misses;
    }
}

void disaggResetStats(void) {
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    memset(&g_alloc_stats, 0, sizeof(g_alloc_stats));
    
    if (g_lru_cache) {
        g_lru_cache->reset_stats();
    }
}

void disaggPrintStats(void) {
    lru_alloc_stats_t stats;
    disaggGetStats(&stats);
    
    printf("=== Allocation Statistics ===\n");
    printf("Total allocated: %zu bytes\n", stats.total_allocated);
    printf("Total freed: %zu bytes\n", stats.total_freed);
    printf("Current usage: %zu bytes\n", stats.current_usage);
    printf("Peak usage: %zu bytes\n", stats.peak_usage);
    printf("Allocation count: %zu\n", stats.allocation_count);
    printf("Free count: %zu\n", stats.free_count);
    printf("Cache hits: %zu\n", stats.cache_hits);
    printf("Cache misses: %zu\n", stats.cache_misses);
    printf("============================\n");
}

// Memory validation and debugging
bool disaggIsValid(void* gaddr) {
    if (!gaddr) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    return g_allocation_sizes.find(gaddr) != g_allocation_sizes.end();
}

bool disaggIsAllocated(void* gaddr) {
    return disaggIsValid(gaddr);
}

void disaggCheckLeaks(void) {
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    
    if (!g_allocation_sizes.empty()) {
        printf("Memory leaks detected:\n");
        for (const auto& pair : g_allocation_sizes) {
            printf("  Address: %p, Size: %zu bytes\n", pair.first, pair.second);
        }
    } else {
        printf("No memory leaks detected.\n");
    }
}

void disaggDumpAllocations(void) {
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    
    printf("Current allocations:\n");
    for (const auto& pair : g_allocation_sizes) {
        lru_mem_type_t type = LRU_MEM_TYPE_DEFAULT;
        auto type_it = g_allocation_types.find(pair.first);
        if (type_it != g_allocation_types.end()) {
            type = type_it->second;
        }
        
        printf("  Address: %p, Size: %zu bytes, Type: %d\n", 
               pair.first, pair.second, static_cast<int>(type));
    }
}

// Advanced memory operations
void disaggPrefault(void* gaddr, size_t size) {
    if (g_lru_cache) {
        GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
        g_lru_cache->prefetch(addr, size);
    }
}

void disaggAdvise(void* gaddr, size_t size, int advice) {
    // Memory advice - placeholder for future implementation
    // Could implement MADV_SEQUENTIAL, MADV_RANDOM, etc.
}

void disaggPin(void* gaddr, size_t size) {
    if (g_lru_cache) {
        GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
        g_lru_cache->pin(addr);
    }
}

void disaggUnpin(void* gaddr) {
    if (g_lru_cache) {
        GlobalAddr addr = GlobalAddr::fromPointer(gaddr);
        g_lru_cache->unpin(addr);
    }
}

// Initialization and cleanup
int lru_alloc_init(const char* memory_server_addr) {
    try {
        g_lru_cache = std::make_unique<LRUCache>(memory_server_addr);
        return 0;
    } catch (const std::exception& e) {
        fprintf(stderr, "Failed to initialize LRU allocator: %s\n", e.what());
        return -1;
    }
}

void lru_alloc_cleanup(void) {
    if (g_lru_cache) {
        g_lru_cache->flush_all();
        g_lru_cache.reset();
    }
    
    std::lock_guard<std::mutex> lock(g_alloc_mutex);
    g_allocation_sizes.clear();
    g_allocation_types.clear();
    memset(&g_alloc_stats, 0, sizeof(g_alloc_stats));
}

bool lru_alloc_is_initialized(void) {
    return g_lru_cache != nullptr;
}

}  // extern "C"
