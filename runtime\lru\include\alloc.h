#ifndef LRU_ALLOC_H
#define LRU_ALLOC_H

#include <stddef.h>
#include "addr.h"

#ifdef __cplusplus
extern "C" {
#endif

// Core allocation functions compatible with compute/include/alloc.h
void* disaggAlloc(size_t size);
void disaggFree(void* gaddr);

// Extended allocation functions for LRU cache management
void* disaggAllocCached(size_t size);        // Allocate and immediately cache
void* disaggAllocAligned(size_t size, size_t alignment);  // Aligned allocation
void* disaggRealloc(void* gaddr, size_t new_size);       // Reallocate memory

// Bulk allocation operations
void** disaggAllocBulk(size_t* sizes, size_t count);     // Allocate multiple blocks
void disaggFreeBulk(void** gaddrs, size_t count);        // Free multiple blocks

// Memory type management
typedef enum {
    LRU_MEM_TYPE_DEFAULT = 0,
    LRU_MEM_TYPE_SMALL = 1,      // Small objects (< 1KB)
    LRU_MEM_TYPE_MEDIUM = 2,     // Medium objects (1KB - 64KB)
    LRU_MEM_TYPE_LARGE = 3,      // Large objects (> 64KB)
    LRU_MEM_TYPE_PERSISTENT = 4, // Persistent/pinned memory
    LRU_MEM_TYPE_TEMPORARY = 5   // Temporary/scratch memory
} lru_mem_type_t;

void* disaggAllocTyped(size_t size, lru_mem_type_t type);
void disaggFreeTyped(void* gaddr, lru_mem_type_t type);

// Memory information and statistics
size_t disaggGetSize(void* gaddr);           // Get size of allocated block
size_t disaggGetAlignment(void* gaddr);      // Get alignment of allocated block
lru_mem_type_t disaggGetType(void* gaddr);   // Get memory type

// Allocation statistics
typedef struct {
    size_t total_allocated;      // Total bytes allocated
    size_t total_freed;          // Total bytes freed
    size_t current_usage;        // Current memory usage
    size_t peak_usage;           // Peak memory usage
    size_t allocation_count;     // Number of allocations
    size_t free_count;           // Number of frees
    size_t cache_hits;           // Cache hits during allocation
    size_t cache_misses;         // Cache misses during allocation
} lru_alloc_stats_t;

void disaggGetStats(lru_alloc_stats_t* stats);
void disaggResetStats(void);
void disaggPrintStats(void);

// Memory pool management
typedef struct lru_memory_pool lru_memory_pool_t;

lru_memory_pool_t* lru_pool_create(size_t pool_size, size_t block_size);
void lru_pool_destroy(lru_memory_pool_t* pool);
void* lru_pool_alloc(lru_memory_pool_t* pool);
void lru_pool_free(lru_memory_pool_t* pool, void* ptr);

// Memory validation and debugging
bool disaggIsValid(void* gaddr);             // Check if address is valid
bool disaggIsAllocated(void* gaddr);         // Check if address is allocated
void disaggCheckLeaks(void);                 // Check for memory leaks
void disaggDumpAllocations(void);            // Dump all current allocations

// Advanced memory operations
void disaggPrefault(void* gaddr, size_t size);  // Prefault memory pages
void disaggAdvise(void* gaddr, size_t size, int advice);  // Memory advice
void disaggPin(void* gaddr, size_t size);       // Pin memory in cache
void disaggUnpin(void* gaddr);                  // Unpin memory

// Initialization and cleanup
int lru_alloc_init(const char* memory_server_addr);
void lru_alloc_cleanup(void);
bool lru_alloc_is_initialized(void);

#ifdef __cplusplus
}
#endif

#endif  // LRU_ALLOC_H
