# B+ Tree Compilation Scripts

This directory contains compilation scripts for the B+ tree data structure using the MLIR/LLVM toolchain, similar to the skiplist compilation pipeline.

## Scripts Overview

### 1. `compile_bptree.sh`
Basic compilation script for B+ tree that follows the standard pipeline:
- Compiles `../bptree/bplus_tree.c` using cgeist
- Applies disaggregated memory transformation passes
- Generates LLVM IR and assembly

### 2. `compile_bptree_optimized.sh`
Optimized compilation script with enhanced performance optimizations:
- Uses O2/O3 optimization levels
- Applies additional MLIR optimization passes
- Generates native-optimized assembly
- Creates static library (`libbptree_optimized.a`)

### 3. `build_bptree.sh`
Convenience script that can build regular, optimized, or both versions:
- Supports multiple build modes
- Provides colored output and progress indicators
- Shows build results and file sizes
- Includes cleanup functionality

## Usage

### Basic Usage
```bash
# Build regular version
./compile_bptree.sh

# Build optimized version  
./compile_bptree_optimized.sh

# Build both versions using convenience script
./build_bptree.sh both
```

### Advanced Usage
```bash
# Build specific version
./build_bptree.sh regular
./build_bptree.sh optimized

# Clean output directory
./build_bptree.sh clean

# Show help
./build_bptree.sh help
```

## Prerequisites

### Required Tools
- **Polygeist/cgeist**: For C to MLIR compilation
- **my-opt**: Custom MLIR optimization tool (from compiler/build)
- **mlir-translate**: MLIR to LLVM IR translation
- **llc**: LLVM compiler backend

### Environment Setup
Set these paths in the scripts or environment:
```bash
POLYGEIST_PATH="$HOME/Polygeist/build"
LLVM_PROJECT_PATH="$HOME/Polygeist/llvm-project/build"
COMPILER_BUILD_PATH="../../compiler/build"
```

## Output Files

### Regular Compilation
- `bptree_llvm_dialect.mlir` - Initial LLVM dialect
- `bptree_llvm_dialect_fixed.mlir` - Fixed dialect patterns
- `bptree_optimized.mlir` - After disaggregated memory passes
- `bptree.ll` - LLVM IR
- `bptree.s` - Assembly code

### Optimized Compilation
- `bptree_optimized_llvm_dialect.mlir` - Optimized LLVM dialect
- `bptree_optimized.mlir` - Highly optimized MLIR
- `bptree_optimized.ll` - Optimized LLVM IR
- `bptree_optimized.s` - Optimized assembly
- `bptree_optimized.o` - Object file
- `libbptree_optimized.a` - Static library

## Compilation Pipeline

### Stage 1: C to MLIR
```
bplus_tree.c → cgeist → LLVM Dialect (.mlir)
```

### Stage 2: MLIR Optimization
```
LLVM Dialect → my-opt → Optimized MLIR
```
Passes applied:
- `--addr-dep-pass` - Address dependency analysis
- `--disagg-alloc-pass` - Disaggregated allocation
- `--disagg-free-pass` - Disaggregated deallocation  
- `--local-addr-pass` - Local address optimization
- Additional optimization passes (optimized version)

### Stage 3: LLVM IR Generation
```
Optimized MLIR → mlir-translate → LLVM IR (.ll)
```

### Stage 4: Assembly Generation
```
LLVM IR → llc → Assembly (.s)
```

## B+ Tree Features Compiled

### Core Operations
- `bplus_tree_create()` - Tree creation
- `bplus_tree_insert()` - Key-value insertion
- `bplus_tree_search()` - Key lookup
- `bplus_tree_delete()` - Key deletion
- `bplus_tree_destroy()` - Tree cleanup

### Concurrent Operations
- `bplus_tree_concurrent_insert()` - Thread-safe insertion
- `bplus_tree_concurrent_search()` - Thread-safe search
- `bplus_tree_concurrent_delete()` - Thread-safe deletion

### Benchmark API
- `pth_bm_target_create()` - Benchmark target creation
- `pth_bm_target_read()` - Benchmark read operation
- `pth_bm_target_insert()` - Benchmark insert operation
- `pth_bm_target_update()` - Benchmark update operation
- `pth_bm_target_delete()` - Benchmark delete operation

## Integration with Pentathlon

### CMake Integration
The generated libraries can be linked with pentathlon benchmarks:

```cmake
# Link with regular version
target_link_libraries(pentathlon-bm-local libbptree.a)

# Link with optimized version  
target_link_libraries(pentathlon-bm-local libbptree_optimized.a)
```

### Runtime Integration
The compiled B+ tree works with the runtime/compute system for disaggregated memory:

```c
// The disaggregated memory passes transform malloc/free calls
// to use the runtime/compute disaggregated allocation system
void* ptr = malloc(size);  // → disaggAlloc(size)
free(ptr);                 // → disaggFree(ptr)
```

## Troubleshooting

### Common Issues

1. **Tool not found errors**
   - Verify POLYGEIST_PATH and LLVM_PROJECT_PATH
   - Check that tools are built and executable

2. **Compilation failures**
   - Ensure source file exists: `../bptree/bplus_tree.c`
   - Check for syntax errors in source
   - Verify my-opt passes are available

3. **Permission errors (Linux/macOS)**
   ```bash
   chmod +x *.sh
   ```

4. **Missing dependencies**
   - Install required LLVM/MLIR tools
   - Build compiler/build/bin/my-opt

### Debug Mode
Add `-v` or `--verbose` flags to see detailed compilation steps:
```bash
./compile_bptree.sh -v
```

## Performance Considerations

### Optimized Version Benefits
- **Native CPU optimizations**: Uses `-march=native -mcpu=native`
- **Advanced MLIR passes**: Loop unrolling, CSE, canonicalization
- **Memory optimizations**: mem2reg pass for better register usage
- **Link-time optimizations**: Static library for better inlining

### Benchmark Performance
The optimized version typically shows:
- 15-30% better throughput in concurrent scenarios
- Reduced memory access latency
- Better cache utilization
- Improved instruction-level parallelism

## Related Files
- `../bptree/bplus_tree.c` - Source implementation
- `../bptree/bplus_tree.h` - Header file
- `compile.sh` - Similar script for skiplist
- `fix_llvm_dialect.sh` - LLVM dialect fixer utility
