#!/bin/bash

# Optimized compilation script for B+ tree using MLIR/LLVM toolchain
# This version includes additional optimizations for performance benchmarks
# Pipeline:
# 1. Use cgeist to compile B+ tree to LLVM Dialect
# 2. Use my-opt to transform LLVM Dialect with optimization passes
# 3. Use mlir-translate to transform LLVM Dialect to LLVM IR
# 4. Use llc to transform LLVM IR to optimized assembly

set -e  # Exit on any error

# Configuration
POLYGEIST_PATH="$HOME/Polygeist/build"
LLVM_PROJECT_PATH="$HOME/Polygeist/llvm-project/build"
COMPILER_BUILD_PATH="../../compiler/build"  # Relative to this script location
OUTPUT_DIR="./output"
BPTREE_DIR="../bptree"

# Tool paths
CGEIST="$POLYGEIST_PATH/bin/cgeist"
MY_OPT="$COMPILER_BUILD_PATH/bin/my-opt"
MLIR_TRANSLATE="$LLVM_PROJECT_PATH/bin/mlir-translate"
LLC="$LLVM_PROJECT_PATH/bin/llc"

# Optimized flags
CGEIST_FLAGS="-emit-llvm-dialect -S -function=* -O2"
# Extended passes for optimization + disaggregated memory
MY_OPT_FLAGS="--addr-dep-pass --disagg-alloc-pass --disagg-free-pass --local-addr-pass --canonicalize --cse --loop-unroll --mem2reg"
LLC_FLAGS="-O3 -march=native -mcpu=native"

# Source files
BPTREE_SOURCES="$BPTREE_DIR/bplus_tree.c"

# Output files (optimized versions)
LLVM_DIALECT_OUTPUT="$OUTPUT_DIR/bptree_optimized_llvm_dialect.mlir"
LLVM_DIALECT_FIXED="$OUTPUT_DIR/bptree_optimized_llvm_dialect_fixed.mlir"
OPTIMIZED_OUTPUT="$OUTPUT_DIR/bptree_optimized.mlir"
LLVM_IR_OUTPUT="$OUTPUT_DIR/bptree_optimized.ll"
ASSEMBLY_OUTPUT="$OUTPUT_DIR/bptree_optimized.s"
OBJECT_OUTPUT="$OUTPUT_DIR/bptree_optimized.o"
LIBRARY_OUTPUT="$OUTPUT_DIR/libbptree_optimized.a"

# Create output directory
mkdir -p "$OUTPUT_DIR"

echo "=== B+ Tree Optimized Compilation Pipeline ==="
echo "Source: $BPTREE_SOURCES"
echo "Output directory: $OUTPUT_DIR"
echo "Optimization level: High (O2/O3 with native optimizations)"
echo

# Check if tools exist
echo "Checking tool availability..."
for tool in "$CGEIST" "$MY_OPT" "$MLIR_TRANSLATE" "$LLC"; do
    if [[ ! -x "$tool" ]]; then
        echo "Error: Tool not found or not executable: $tool"
        exit 1
    fi
done
echo "All tools found."
echo

# Step 1: Use cgeist to compile B+ tree to LLVM Dialect with optimizations
echo "Step 1: Compiling C source to LLVM Dialect using cgeist (optimized)..."
echo "Command: $CGEIST $CGEIST_FLAGS $BPTREE_SOURCES > $LLVM_DIALECT_OUTPUT"
if ! "$CGEIST" $CGEIST_FLAGS "$BPTREE_SOURCES" > "$LLVM_DIALECT_OUTPUT"; then
    echo "Error: cgeist compilation failed"
    exit 1
fi
echo "✓ Optimized LLVM Dialect generated: $LLVM_DIALECT_OUTPUT"
echo

# Step 1.5: Fix LLVM Dialect (replace [] with [0] in llvm.getelement)
echo "Step 1.5: Fixing LLVM Dialect patterns..."
SCRIPT_DIR="$(dirname "$0")"
if [[ -x "$SCRIPT_DIR/fix_llvm_dialect.sh" ]]; then
    echo "Running LLVM dialect fixer..."
    "$SCRIPT_DIR/fix_llvm_dialect.sh" -i "$LLVM_DIALECT_OUTPUT" -o "$LLVM_DIALECT_FIXED" -v
    echo "✓ LLVM Dialect fixed: $LLVM_DIALECT_FIXED"
    # Use the fixed file for subsequent steps
    LLVM_DIALECT_FOR_OPT="$LLVM_DIALECT_FIXED"
else
    echo "Warning: fix_llvm_dialect.sh not found or not executable, using original file"
    LLVM_DIALECT_FOR_OPT="$LLVM_DIALECT_OUTPUT"
fi
echo

# Step 2: Use my-opt to transform LLVM Dialect with extended optimizations
echo "Step 2: Optimizing LLVM Dialect using my-opt (extended passes)..."
echo "Command: $MY_OPT $MY_OPT_FLAGS $LLVM_DIALECT_FOR_OPT -o $OPTIMIZED_OUTPUT"
if ! "$MY_OPT" $MY_OPT_FLAGS "$LLVM_DIALECT_FOR_OPT" -o "$OPTIMIZED_OUTPUT"; then
    echo "Error: my-opt optimization failed"
    exit 1
fi
echo "✓ Highly optimized LLVM Dialect generated: $OPTIMIZED_OUTPUT"
echo

# Step 3: Use mlir-translate to transform LLVM Dialect to LLVM IR
echo "Step 3: Translating LLVM Dialect to LLVM IR using mlir-translate..."
echo "Command: $MLIR_TRANSLATE --mlir-to-llvmir $OPTIMIZED_OUTPUT -o $LLVM_IR_OUTPUT"
if ! "$MLIR_TRANSLATE" --mlir-to-llvmir "$OPTIMIZED_OUTPUT" -o "$LLVM_IR_OUTPUT"; then
    echo "Error: mlir-translate failed"
    exit 1
fi
echo "✓ Optimized LLVM IR generated: $LLVM_IR_OUTPUT"
echo

# Step 4: Use llc to transform LLVM IR to optimized assembly
echo "Step 4: Compiling LLVM IR to optimized assembly using llc..."
echo "Command: $LLC $LLC_FLAGS $LLVM_IR_OUTPUT -o $ASSEMBLY_OUTPUT"
if ! "$LLC" $LLC_FLAGS "$LLVM_IR_OUTPUT" -o "$ASSEMBLY_OUTPUT"; then
    echo "Error: llc compilation failed"
    exit 1
fi
echo "✓ Optimized assembly generated: $ASSEMBLY_OUTPUT"
echo

# Step 5: Create object file and static library
echo "Step 5: Creating object file and static library..."
if command -v gcc >/dev/null 2>&1; then
    echo "Creating object file..."
    if gcc -c -O3 -march=native "$ASSEMBLY_OUTPUT" -o "$OBJECT_OUTPUT"; then
        echo "✓ Object file created: $OBJECT_OUTPUT"
        
        echo "Creating static library..."
        if ar rcs "$LIBRARY_OUTPUT" "$OBJECT_OUTPUT"; then
            echo "✓ Static library created: $LIBRARY_OUTPUT"
        else
            echo "Warning: Failed to create static library"
        fi
    else
        echo "Warning: Failed to create object file"
    fi
else
    echo "Warning: gcc not found, skipping object file and library creation"
fi
echo

echo "=== Optimized Compilation Pipeline Complete ==="
echo "Generated files:"
echo "  - LLVM Dialect (original): $LLVM_DIALECT_OUTPUT"
if [[ -f "$LLVM_DIALECT_FIXED" ]]; then
    echo "  - LLVM Dialect (fixed): $LLVM_DIALECT_FIXED"
fi
echo "  - Optimized LLVM Dialect: $OPTIMIZED_OUTPUT"
echo "  - Optimized LLVM IR: $LLVM_IR_OUTPUT"
echo "  - Optimized Assembly: $ASSEMBLY_OUTPUT"
if [[ -f "$OBJECT_OUTPUT" ]]; then
    echo "  - Object file: $OBJECT_OUTPUT"
fi
if [[ -f "$LIBRARY_OUTPUT" ]]; then
    echo "  - Static library: $LIBRARY_OUTPUT"
fi
echo

# Optional: Show file sizes
echo "File sizes:"
FILES_TO_CHECK=("$LLVM_DIALECT_OUTPUT" "$OPTIMIZED_OUTPUT" "$LLVM_IR_OUTPUT" "$ASSEMBLY_OUTPUT")
if [[ -f "$LLVM_DIALECT_FIXED" ]]; then
    FILES_TO_CHECK+=("$LLVM_DIALECT_FIXED")
fi
if [[ -f "$OBJECT_OUTPUT" ]]; then
    FILES_TO_CHECK+=("$OBJECT_OUTPUT")
fi
if [[ -f "$LIBRARY_OUTPUT" ]]; then
    FILES_TO_CHECK+=("$LIBRARY_OUTPUT")
fi

for file in "${FILES_TO_CHECK[@]}"; do
    if [[ -f "$file" ]]; then
        size=$(wc -c < "$file")
        echo "  $(basename "$file"): $size bytes"
    fi
done

echo
echo "=== B+ Tree Optimized Compilation Summary ==="
echo "Source file: $BPTREE_SOURCES"
echo "Target: High-performance B+ Tree with disaggregated memory support"
echo "Optimizations applied:"
echo "  - Compiler optimizations: O2/O3 with native CPU features"
echo "  - MLIR passes: canonicalize, CSE, loop unroll, mem2reg"
echo "  - Disaggregated memory passes: addr-dep, disagg-alloc/free, local-addr"
echo "  - Native architecture optimizations"
echo
echo "Performance features:"
echo "  - Thread-safe concurrent operations"
echo "  - Optimized memory access patterns"
echo "  - Cache-friendly data structures"
echo "  - Vectorized operations where possible"
echo
echo "Usage:"
echo "  - Link $LIBRARY_OUTPUT with pentathlon benchmarks"
echo "  - Use for high-performance B+ tree benchmarks"
echo "  - Compatible with runtime/compute disaggregated memory system"
