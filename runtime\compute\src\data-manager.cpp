#include <algorithm>
#include <atomic>
#include <cstdint>
#include <stdexcept>

#include "addr.h"
#include "common/handshake.h"
#include "data-manager.hpp"

void DataManager::disaggFree(GlobalAddr gaddr) {
    // Implementation of disaggregated free
    // This function should handle the freeing of the memory associated with the given global
    // address It should also update the cache and any necessary data structures Check if the
    // address is valid
    if (gaddr == GlobalAddr::null()) {
        // Invalid address
        fprintf(stderr, "Error: Invalid address\n");
        return;
    }
    addr_table_mutex.lock();
    if (addr_table.find(gaddr) == addr_table.end()) {
        // Address not found in the cache
        addr_table_mutex.unlock();
        fprintf(stderr, "Error: Address not found in address table\n");
        return;
    }
    auto node = addr_table[gaddr];
    if (node == nullptr) {
        // Address not found in the address table
        addr_table_mutex.unlock();
        fprintf(stderr, "Error: Address not found in address table\n");
        return;
    }
    addr_table_mutex.unlock();
    // Lock the node for thread safety
    // Free the memory associated with the global address
    node->mutex.lock();
    void* local_addr = node->local_addr;
    if (local_addr == nullptr) {
        cacheInsert(gaddr);  // Insert into cache if not already present
        local_addr = getLocalAddr(gaddr);  // Get the local address
        if (local_addr == nullptr) {
            node->mutex.unlock();
            fprintf(stderr, "Error: Local address not found\n");
            return;  // Local address not found
        }
        node->local_addr = local_addr;  // Set the local address
    }
    node->mutex.unlock();
    node->mutex.lock();
    for (auto& child : node->children) {
        addr_table_mutex.lock();
        auto v = addr_table[child];
        addr_table_mutex.unlock();
        if (v != nullptr) {
            v->mutex.lock();  // Lock the child node
            v->parents.remove(gaddr);  // Remove from the child's parent list
            v->mutex.unlock();  // Unlock the child node
        }
    }
    for (auto& parent : node->parents) {
        addr_table_mutex.lock();
        auto v = addr_table[parent];
        addr_table_mutex.unlock();
        if (v != nullptr) {
            v->mutex.lock();  // Lock the parent node
            v->children.remove(gaddr);  // Remove from the parent's children list
            v->cache_children_num--;
            if (v->cache_children_num == 0) {
                cache_leaf_mutex.lock();
                cache_leaf[parent.typeID].push_back(parent);
                cache_leaf_mutex.unlock();
            }
            v->mutex.unlock();  // Unlock the parent node
        }
    }
    node->mutex.unlock();  // Unlock the node
    free(node);  // Free the node
    free(local_addr);  // Free the local address
    // TODO: Free on memory side
    // This is currently no-op
    addr_table_mutex.lock();
    addr_table.erase(gaddr);  // Remove from the address table
    addr_table_mutex.unlock();
    cache_size -= get_size(gaddr);  // Decrease cache size
}

// LZL: Let's assume each time malloc() allocates exactly one such structure
// Probably should check the passed size with type size in compiler, if not matching then ignore it
GlobalAddr DataManager::disaggAlloc(size_t size) {
    // Implementation of disaggregated allocation
    // This function should handle the allocation of memory of the given size
    // It should return a global address that represents the allocated memory
    GlobalAddr gaddr = GlobalAddr::null();
    gaddr.typeID = get_type_id(size);

    auto& chunk_next_addrs = types[gaddr.typeID].chunk_next_addrs;

    // Check if there's still enough room in existing chunk
    if (chunk_next_addrs.empty() ||
        (chunk_next_addrs.back() & (chunk_size() - 1)) + size > chunk_size()) {
        // if not, get new chunk and allocate from it
        uint64_t addr = next_chunk_addr.fetch_add(chunk_size());
        chunk_next_addrs.push_back(addr);
        if (addr >= mem_upper_bound(rdma->mem()))
            throw std::runtime_error("out of memory in memory side");
    }

    gaddr.offset = chunk_next_addrs.back();
    chunk_next_addrs.back() += size;

    // Allocate memory for the node
    auto node = new NodeType();
    // node->local_addr = nullptr;
    // node->freq = 0;
    // node->cache_children_num = 0;
    // node->mutex = std::mutex();
    // node->children = std::list<GlobalAddr>();
    // node->parents = std::list<GlobalAddr>();
    addr_table_mutex.lock();
    addr_table[gaddr] = node;  // Insert the node into the address table
    addr_table_mutex.unlock();
    cacheInsert(gaddr);        // Insert the address into the cache
    return gaddr;
}

void DataManager::addAddrDep(GlobalAddr addr_u, GlobalAddr addr_v) {
    // Implementation of adding address dependency
    // This function should handle the addition of a dependency between two global addresses
    acc_dep_mutex.lock();
    temp_access_dep[addr_u] = addr_v;  // Store the dependency
    acc_dep_mutex.unlock();
}

void DataManager::acceptAddrDep(GlobalAddr addr) {
    // Implementation of accepting address dependency
    // This function should handle the acceptance of a dependency for a given global address
    acc_dep_mutex.lock();
    if (temp_access_dep.find(addr) != temp_access_dep.end()) {
        GlobalAddr addr_v = get_orig_global_addr(temp_access_dep[addr]);
        GlobalAddr addr_u = get_orig_global_addr(addr);
        temp_access_dep.erase(addr);
        addr_table_mutex.lock();
        auto node_v = addr_table[addr_v];
        auto node_u = addr_table[addr_u];
        addr_table_mutex.unlock();
        if (node_v == nullptr) {
            acc_dep_mutex.unlock();
            return;  // addr_v is not in the address table
        }
        if (node_u == nullptr) {
            acc_dep_mutex.unlock();
            return;  // addr_u is not in the address table
        }
        // check if v is u's parent
        node_u->mutex.lock();
        if (std::find(node_u->parents.begin(), node_u->parents.end(), addr_v) != node_u->parents.end()) {
            node_u->mutex.unlock();
            acc_dep_mutex.unlock();
            return;  // addr_v is already a parent of addr_u
        }
        // add u -> v dependency
        node_u->mutex.unlock();
        node_v->mutex.lock();
        node_u->mutex.lock();
        node_v->parents.push_back(addr_u);  // Add addr_u as a parent of addr_v
        node_u->children.push_back(addr_v);  // Add addr_v as a child of addr_u
        if (node_v->local_addr != nullptr) {
            node_u->cache_children_num++;
            if (node_u->cache_children_num == 1) {
                cache_leaf_mutex.lock();
                cache_leaf[addr_u.typeID].remove(addr_u);  // Remove addr_u from the leaf
                cache_leaf_mutex.unlock();
            }
        }
        node_u->mutex.unlock();
        node_v->mutex.unlock();
    }
    acc_dep_mutex.unlock();
}

void* DataManager::getLocalAddr(GlobalAddr gaddr) {
    // Implementation of getting local address
    // This function should return the local address associated with the given global address
    auto offset = get_offset(gaddr);
    gaddr = get_orig_global_addr(gaddr);
    addr_table_mutex.lock();
    if (addr_table.find(gaddr) != addr_table.end()) {
        auto node = addr_table[gaddr];
        addr_table_mutex.unlock();
        if (node == nullptr) {
            return nullptr;  // Address not found in the address table
        }
        node->mutex.lock();
        auto local_addr = node->local_addr;
        node->mutex.unlock();
        return local_addr + offset;  // Return the local address
    }
    addr_table_mutex.unlock();
    return nullptr;  // Address not found
}

bool DataManager::cacheInsert(GlobalAddr gaddr) {
    // Implementation of cache insertion
    // This function should handle the insertion of a global address into the cache
    // Check if the address is already in the cache
    gaddr = get_orig_global_addr(gaddr);
    addr_table_mutex.lock();
    auto node = addr_table[gaddr];
    addr_table_mutex.unlock();
    if (node == nullptr) {
        return false;  // Address not found in the address table
    }
    auto local_addr = getLocalAddr(gaddr);
    if (local_addr) {
        // Address is already in cache
        node->mutex.lock();
        node->freq++;  // Increase the frequency of the address
        node->mutex.unlock();
        return true;
    }
    // Insert the address into the cache
    auto size = get_size(gaddr);
    if (!is_cache_full(size)) {
        // Cache is not full, insert the address
        cache_size += size;
        local_addr = malloc(size);  // Allocate memory for the local address
        if (local_addr == nullptr) {
            return false;  // Memory allocation failed
        }
        node->mutex.lock();
        node->freq++;  // Increase the frequency of the address
        node->local_addr = local_addr;  // Allocate memory for the local address
        rdma->read(gaddr, local_addr, size); // Read from memory side
        node->mutex.unlock();
        update_cache_leaf_insert(gaddr);  // Update the cache leaf
        return true;
    }
    // Cache is full, need to evict an address
    cache_leaf_mutex.lock();
    if (!cache_leaf[gaddr.typeID].empty()) {
        // Cache is not empty, evict the oldest address
        auto evict_addr = cache_leaf[gaddr.typeID].front();
        cache_leaf_mutex.unlock();
        update_cache_leaf_remove(evict_addr);  // Update the cache leaf
        auto evict_node = addr_table[evict_addr];
        if (evict_node == nullptr) {
            return false;  // Address not found in the address table
        }
        evict_node->mutex.lock();
        // Check if the evicted address has children
        if (evict_node->cache_children_num > 0) {
            evict_node->mutex.unlock();
            return false;  // Address has children, cannot evict
        }
        // Evict the address from the cache
        local_addr = evict_node->local_addr;
        if (!local_addr) {
            evict_node->mutex.unlock();
            return false;  // Local address not found
        }
        rdma->write(local_addr, evict_addr, get_size(evict_addr));  // Write back to memory side
        evict_node->local_addr = nullptr;  // Remove the local address
        evict_node->mutex.unlock();
        node->mutex.lock();
        node->local_addr = local_addr;  // Assign the evicted address to the new address
        node->freq++;  // Increase the frequency of the address
        rdma->read(gaddr, local_addr, size);  // read from memory side
        node->mutex.unlock();
        update_cache_leaf_insert(gaddr);  // Update the cache leaf
        return true;
    }

    cache_leaf_mutex.unlock();
    while (is_cache_full(size)) {
        GlobalAddr evict_addr;
        size_t evict_size = 0;
        cache_leaf_mutex.lock();
        for (int i = 0; i < cache_leaf.size(); i++) {
            if (cache_leaf[i].empty()) {
                continue;
            }
            auto addr = cache_leaf[i].front();
            if (get_size(addr) > evict_size) {
                evict_size = get_size(addr);
                evict_addr = addr;  // Find the largest address to evict
            }
        }
        cache_leaf_mutex.unlock();
        if (evict_size == 0) {
            return false;  // No address to evict
        }
        addr_table_mutex.lock();
        auto evict_node = addr_table[evict_addr];
        addr_table_mutex.unlock();
        if (evict_node == nullptr) {
            return false;  // Address not found in the address table
        }
        evict_node->mutex.lock();
        // Check if the evicted address has children
        if (evict_node->cache_children_num > 0) {
            evict_node->mutex.unlock();
            continue;  // Address has children, cannot evict
        }
        // Evict the address from the cache
        local_addr = evict_node->local_addr;
        if (!local_addr) {
            evict_node->mutex.unlock();
            return false;  // Local address not found
        }
        evict_node->local_addr = nullptr;  // Remove the local address
        evict_node->mutex.unlock();
        rdma->write(local_addr, evict_addr, evict_size);  // Write back to memory side
        free(local_addr);  // Free the evicted address
        update_cache_leaf_remove(evict_addr);  // Update the cache leaf
        cache_size -= evict_size;  // Decrease cache size
    }
    
    if (!is_cache_full(size)) {
        // Cache is not full, insert the address
        cache_size += size;
        local_addr = malloc(size);  // Allocate memory for the local address
        if (local_addr == nullptr) {
            return false;  // Memory allocation failed
        }
        node->mutex.lock();
        node->freq++;  // Increase the frequency of the address
        node->local_addr = local_addr;  // Allocate memory for the local address
        rdma->read(gaddr, local_addr, size);  // read from memory side
        node->mutex.unlock();
        update_cache_leaf_insert(gaddr);  // Update the cache leaf
        return true;
    }
    return false;
}
