#include <iostream>
#include <cassert>
#include <cstring>
#include <vector>

#include "../include/addr.h"
#include "../include/alloc.h"
#include "../include/cache.h"

void test_basic_allocation() {
    std::cout << "Testing basic allocation..." << std::endl;
    
    // Test basic allocation
    void* ptr1 = disaggAlloc(1024);
    assert(ptr1 != nullptr);
    assert(disaggIsValid(ptr1));
    assert(disaggGetSize(ptr1) == 1024);
    
    // Test typed allocation
    void* ptr2 = disaggAllocTyped(2048, LRU_MEM_TYPE_LARGE);
    assert(ptr2 != nullptr);
    assert(disaggGetType(ptr2) == LRU_MEM_TYPE_LARGE);
    
    // Test reallocation
    void* ptr3 = disaggRealloc(ptr1, 2048);
    assert(ptr3 != nullptr);
    assert(disaggGetSize(ptr3) == 2048);
    
    // Clean up
    disaggFree(ptr2);
    disaggFree(ptr3);
    
    std::cout << "Basic allocation test passed!" << std::endl;
}

void test_bulk_operations() {
    std::cout << "Testing bulk operations..." << std::endl;
    
    size_t sizes[] = {512, 1024, 2048, 4096};
    size_t count = sizeof(sizes) / sizeof(sizes[0]);
    
    void** ptrs = disaggAllocBulk(sizes, count);
    assert(ptrs != nullptr);
    
    for (size_t i = 0; i < count; i++) {
        assert(ptrs[i] != nullptr);
        assert(disaggGetSize(ptrs[i]) == sizes[i]);
    }
    
    disaggFreeBulk(ptrs, count);
    
    std::cout << "Bulk operations test passed!" << std::endl;
}

void test_cache_operations() {
    std::cout << "Testing cache operations..." << std::endl;
    
    // Note: These tests will only work if RDMA is properly initialized
    // For now, we'll test the API without actual RDMA connectivity
    
    // Test cache configuration
    lru_cache_config_t config = {
        .max_size = 1024 * 1024,  // 1MB
        .max_entries = 1000,
        .eviction_policy = LRU_POLICY_STRICT_LRU,
        .write_policy = LRU_WRITE_BACK,
        .enable_prefetch = false,
        .prefetch_distance = 4096,
        .enable_compression = false,
        .compression_ratio = 0.5
    };
    
    // Test cache statistics
    lru_cache_stats_t stats;
    lru_cache_get_stats(&stats);
    
    std::cout << "Cache operations test completed!" << std::endl;
}

void test_address_operations() {
    std::cout << "Testing address operations..." << std::endl;
    
    void* ptr = disaggAlloc(4096);
    assert(ptr != nullptr);
    
    // Test address operations
    lru_cache_touch(ptr);
    
    // Test cache size and entry count
    size_t cache_size = lru_cache_size();
    size_t entry_count = lru_cache_entry_count();
    double hit_rate = lru_cache_hit_rate();
    
    std::cout << "Cache size: " << cache_size << " bytes" << std::endl;
    std::cout << "Entry count: " << entry_count << std::endl;
    std::cout << "Hit rate: " << (hit_rate * 100.0) << "%" << std::endl;
    
    disaggFree(ptr);
    
    std::cout << "Address operations test passed!" << std::endl;
}

void test_statistics() {
    std::cout << "Testing statistics..." << std::endl;
    
    // Reset statistics
    disaggResetStats();
    lru_cache_reset_stats();
    
    // Allocate some memory
    std::vector<void*> ptrs;
    for (int i = 0; i < 10; i++) {
        void* ptr = disaggAlloc(1024 * (i + 1));
        if (ptr) {
            ptrs.push_back(ptr);
        }
    }
    
    // Print allocation statistics
    disaggPrintStats();
    
    // Print cache statistics
    lru_cache_print_stats();
    
    // Clean up
    for (void* ptr : ptrs) {
        disaggFree(ptr);
    }
    
    // Check for leaks
    disaggCheckLeaks();
    
    std::cout << "Statistics test completed!" << std::endl;
}

void test_advanced_features() {
    std::cout << "Testing advanced features..." << std::endl;
    
    void* ptr = disaggAlloc(8192);
    if (ptr) {
        // Test prefaulting
        disaggPrefault(ptr, 8192);
        
        // Test pinning
        disaggPin(ptr, 8192);
        disaggUnpin(ptr);
        
        // Test cache operations
        lru_cache_mark_dirty(ptr);
        lru_cache_flush(ptr);
        
        disaggFree(ptr);
    }
    
    // Test cache management
    lru_cache_set_max_size(2 * 1024 * 1024);  // 2MB
    size_t max_size = lru_cache_get_max_size();
    std::cout << "Max cache size set to: " << max_size << " bytes" << std::endl;
    
    std::cout << "Advanced features test completed!" << std::endl;
}

int main() {
    std::cout << "=== LRU Cache Manager Tests ===" << std::endl;
    
    try {
        // Note: In a real environment, you would initialize with a valid memory server address
        // For testing purposes, we'll skip RDMA initialization
        std::cout << "Skipping RDMA initialization for testing..." << std::endl;
        
        test_basic_allocation();
        test_bulk_operations();
        test_cache_operations();
        test_address_operations();
        test_statistics();
        test_advanced_features();
        
        std::cout << "\n=== All Tests Completed ===" << std::endl;
        std::cout << "Note: Some tests may show warnings due to missing RDMA connectivity." << std::endl;
        std::cout << "In a production environment, initialize with: lru_alloc_init(\"memory_server_address\")" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    }
}
