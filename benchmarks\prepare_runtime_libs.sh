#!/bin/bash

# <PERSON>ript to build runtime libraries and prepare them for benchmark linking
# This script builds the runtime/compute and runtime/lru modules and copies
# the resulting libraries to runtime/build/ where the benchmark expects them

set -e

SCRIPT_DIR="$(dirname "$0")"
RUNTIME_DIR="$SCRIPT_DIR/../runtime"
RUNTIME_BUILD_DIR="$RUNTIME_DIR/build"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Build runtime libraries for Pentathlon benchmark"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -c, --compute  Build only compute runtime"
    echo "  -l, --lru      Build only LRU runtime"
    echo "  --clean        Clean build directories first"
    echo "  -j N           Number of parallel jobs (default: auto-detect)"
    echo ""
    echo "Examples:"
    echo "  $0              # Build both compute and LRU"
    echo "  $0 --compute    # Build only compute runtime"
    echo "  $0 --lru        # Build only LRU runtime"
    echo "  $0 --clean -j 8 # Clean build with 8 jobs"
}

# Parse arguments
BUILD_COMPUTE=true
BUILD_LRU=true
CLEAN_BUILD=false
JOBS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--compute)
            BUILD_COMPUTE=true
            BUILD_LRU=false
            shift
            ;;
        -l|--lru)
            BUILD_COMPUTE=false
            BUILD_LRU=true
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        -j)
            JOBS="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Auto-detect number of jobs
if [[ -z "$JOBS" ]]; then
    if command -v nproc &> /dev/null; then
        JOBS=$(nproc)
    else
        JOBS=4
    fi
fi

print_header "Pentathlon Runtime Libraries Builder"
echo "Build compute: $BUILD_COMPUTE"
echo "Build LRU: $BUILD_LRU"
echo "Clean build: $CLEAN_BUILD"
echo "Jobs: $JOBS"
echo ""

# Create runtime build directory
mkdir -p "$RUNTIME_BUILD_DIR"

# Clean if requested
if [[ "$CLEAN_BUILD" == "true" ]]; then
    print_header "Cleaning Build Directories"
    
    if [[ -d "$RUNTIME_DIR/compute/build" ]]; then
        rm -rf "$RUNTIME_DIR/compute/build"
        print_success "Cleaned compute build directory"
    fi
    
    if [[ -d "$RUNTIME_DIR/lru/build" ]]; then
        rm -rf "$RUNTIME_DIR/lru/build"
        print_success "Cleaned LRU build directory"
    fi
    
    echo ""
fi

# Build compute runtime
if [[ "$BUILD_COMPUTE" == "true" ]]; then
    print_header "Building Runtime/Compute"
    
    COMPUTE_DIR="$RUNTIME_DIR/compute"
    COMPUTE_BUILD_DIR="$COMPUTE_DIR/build"
    
    if [[ ! -d "$COMPUTE_DIR" ]]; then
        print_error "Runtime/compute directory not found: $COMPUTE_DIR"
        exit 1
    fi
    
    mkdir -p "$COMPUTE_BUILD_DIR"
    cd "$COMPUTE_BUILD_DIR"
    
    echo "Configuring compute runtime..."
    if ! cmake ..; then
        print_error "Failed to configure compute runtime"
        exit 1
    fi
    
    echo "Building compute runtime with $JOBS jobs..."
    if ! make -j "$JOBS"; then
        print_error "Failed to build compute runtime"
        exit 1
    fi
    
    # Copy library to runtime/build
    if [[ -f "libdm_compiler_rt_compute.a" ]]; then
        cp "libdm_compiler_rt_compute.a" "$RUNTIME_BUILD_DIR/"
        print_success "Copied libdm_compiler_rt_compute.a to $RUNTIME_BUILD_DIR/"
    else
        print_error "libdm_compiler_rt_compute.a not found after build"
        exit 1
    fi
    
    cd - > /dev/null
    echo ""
fi

# Build LRU runtime
if [[ "$BUILD_LRU" == "true" ]]; then
    print_header "Building Runtime/LRU"
    
    LRU_DIR="$RUNTIME_DIR/lru"
    LRU_BUILD_DIR="$LRU_DIR/build"
    
    if [[ ! -d "$LRU_DIR" ]]; then
        print_error "Runtime/lru directory not found: $LRU_DIR"
        exit 1
    fi
    
    mkdir -p "$LRU_BUILD_DIR"
    cd "$LRU_BUILD_DIR"
    
    echo "Configuring LRU runtime..."
    if ! cmake ..; then
        print_error "Failed to configure LRU runtime"
        exit 1
    fi
    
    echo "Building LRU runtime with $JOBS jobs..."
    if ! make -j "$JOBS"; then
        print_error "Failed to build LRU runtime"
        exit 1
    fi
    
    # Copy library to runtime/build
    if [[ -f "liblru-cache.a" ]]; then
        cp "liblru-cache.a" "$RUNTIME_BUILD_DIR/"
        print_success "Copied liblru-cache.a to $RUNTIME_BUILD_DIR/"
    else
        print_error "liblru-cache.a not found after build"
        exit 1
    fi
    
    cd - > /dev/null
    echo ""
fi

# Verify libraries
print_header "Verifying Libraries"

COMPUTE_LIB="$RUNTIME_BUILD_DIR/libdm_compiler_rt_compute.a"
LRU_LIB="$RUNTIME_BUILD_DIR/liblru-cache.a"

if [[ "$BUILD_COMPUTE" == "true" ]]; then
    if [[ -f "$COMPUTE_LIB" ]]; then
        SIZE=$(stat -c%s "$COMPUTE_LIB" 2>/dev/null || stat -f%z "$COMPUTE_LIB" 2>/dev/null || echo "unknown")
        print_success "Compute library: $COMPUTE_LIB ($SIZE bytes)"
    else
        print_error "Compute library not found: $COMPUTE_LIB"
    fi
fi

if [[ "$BUILD_LRU" == "true" ]]; then
    if [[ -f "$LRU_LIB" ]]; then
        SIZE=$(stat -c%s "$LRU_LIB" 2>/dev/null || stat -f%z "$LRU_LIB" 2>/dev/null || echo "unknown")
        print_success "LRU library: $LRU_LIB ($SIZE bytes)"
    else
        print_error "LRU library not found: $LRU_LIB"
    fi
fi

echo ""
print_header "Build Summary"
echo "Runtime build directory: $RUNTIME_BUILD_DIR"
echo "Available libraries:"

if [[ -f "$COMPUTE_LIB" ]]; then
    echo "  ✓ libdm_compiler_rt_compute.a"
else
    echo "  ✗ libdm_compiler_rt_compute.a"
fi

if [[ -f "$LRU_LIB" ]]; then
    echo "  ✓ liblru-cache.a"
else
    echo "  ✗ liblru-cache.a"
fi

echo ""
echo "You can now build the benchmark with:"
echo "  cd benchmarks"
echo "  mkdir build && cd build"
echo "  cmake -DPENTATHLON_LINK_COMPUTE=ON -DPENTATHLON_LINK_LRU=ON .."
echo "  make benchmark-optimized"
echo "  make lru"
echo ""
echo "Or use the build script:"
echo "  ./build_targets.sh benchmark-optimized"
echo "  ./build_targets.sh lru"

print_success "Runtime libraries preparation completed!"
