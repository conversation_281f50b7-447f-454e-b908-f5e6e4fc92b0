cmake_minimum_required(VERSION 3.20)
project(LRUCacheManager)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
pkg_check_modules(IBVERBS REQUIRED libibverbs)
pkg_check_modules(RDMACM REQUIRED librdmacm)

# Include directories
include_directories(include)
include_directories(../common)

# Source files
set(LRU_SOURCES
    src/lru-cache.cpp
    src/addr.cpp
    src/alloc.cpp
    src/cache.cpp
)

# Create the LRU cache library
add_library(lru-cache STATIC ${LRU_SOURCES})

# Link libraries
target_link_libraries(lru-cache
    ${IBVERBS_LIBRARIES}
    ${RDMACM_LIBRARIES}
    pthread
)

# Compiler flags
target_compile_options(lru-cache PRIVATE
    ${IBVERBS_CFLAGS_OTHER}
    ${RDMACM_CFLAGS_OTHER}
    -Wall
    -Wextra
    -O2
    -g
)

# Add include directories for dependencies
target_include_directories(lru-cache PRIVATE
    ${IBVERBS_INCLUDE_DIRS}
    ${RDMACM_INCLUDE_DIRS}
)

# Create a test executable
add_executable(lru-test
    tests/test_lru.cpp
)

target_link_libraries(lru-test
    lru-cache
    ${IBVERBS_LIBRARIES}
    ${RDMACM_LIBRARIES}
    pthread
)

# Install targets
install(TARGETS lru-cache
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include/lru
    FILES_MATCHING PATTERN "*.h"
)

# Add custom target for running tests
add_custom_target(run-lru-tests
    COMMAND lru-test
    DEPENDS lru-test
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

# Documentation target (if Doxygen is available)
find_package(Doxygen)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    if(EXISTS ${DOXYGEN_IN})
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(lru-docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating LRU cache documentation with Doxygen"
            VERBATIM
        )
    endif()
endif()

# Add compile definitions
target_compile_definitions(lru-cache PRIVATE
    -DLRU_CACHE_VERSION_MAJOR=1
    -DLRU_CACHE_VERSION_MINOR=0
    -DLRU_CACHE_VERSION_PATCH=0
)

# Enable additional warnings for development
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_options(lru-cache PRIVATE
        -Wpedantic
        -Wshadow
        -Wconversion
        -Wunreachable-code
    )
endif()

# Add sanitizers for debug builds
if(CMAKE_BUILD_TYPE STREQUAL "Debug" AND CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    target_compile_options(lru-cache PRIVATE
        -fsanitize=address
        -fsanitize=undefined
        -fno-omit-frame-pointer
    )
    target_link_options(lru-cache PRIVATE
        -fsanitize=address
        -fsanitize=undefined
    )
endif()

# Print configuration summary
message(STATUS "LRU Cache Manager Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  IBVERBS found: ${IBVERBS_FOUND}")
message(STATUS "  RDMACM found: ${RDMACM_FOUND}")
message(STATUS "  Doxygen found: ${DOXYGEN_FOUND}")
