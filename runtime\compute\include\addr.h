#ifndef COMPUTE_GLOBAL_ADDR_H
#define COMPUTE_GLOBAL_ADDR_H

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// LZL: probably just use the raw pointer value (that is directly from memory side) for the lower 56
// bit? In x86 only 48 bits are used, actually There's no point of separating "cache" and "node"
// offset I guess? Also the "swizzled" bit from DEX should be needed I guess? It should be in the
// very top bit.
union GlobalAddr {
    struct {
        uint64_t offset : 56;
        uint8_t typeID : 8;
    };
    uint64_t val;

#ifdef __cplusplus
    static GlobalAddr null() { return {.val = 0}; }
    bool operator==(const GlobalAddr& other) const { return val == other.val; }
    bool operator!=(const GlobalAddr& other) const { return val != other.val; }
    bool operator<(const GlobalAddr& other) const { return val < other.val; }
    // transform to a pointer type
    operator void*() const {
        return reinterpret_cast<void*>(val);
    }
    // transform from a pointer type
    static GlobalAddr fromPointer(void* ptr) {
        GlobalAddr gaddr;
        gaddr.val = reinterpret_cast<uint64_t>(ptr);
        return gaddr;
    }
#endif
};

void* getLocalAddr(void* gaddr);
// LZL: Just return the swizzled bit, could be done in compiler
bool isLocalAddr(void* gaddr);

void addAddrDep(void* addr_u, void* addr_v);
void acceptAddrDep(void* addr);

#ifdef __cplusplus
}

#include <unordered_map>

// Provide hash and equality for global_addr_t if not already defined
namespace std {
template <>
struct hash<GlobalAddr> {
    std::size_t operator()(const GlobalAddr& g) const noexcept {
        // Replace this with an appropriate hash for global_addr_t
        // Assuming global_addr_t has a member 'addr' of type uint64_t
        return std::hash<uint64_t>()(g.val);
    }
};
template <>
struct equal_to<GlobalAddr> {
    bool operator()(const GlobalAddr& lhs, const GlobalAddr& rhs) const noexcept {
        // Replace this with an appropriate equality check for global_addr_t
        return lhs.val == rhs.val;
    }
};
}  // namespace std
#endif

#endif  // _COMPUTE_GLOBAL_ADDR_H_
